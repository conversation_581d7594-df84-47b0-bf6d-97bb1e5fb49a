export interface AmountList {
  /**
   * 币种金额(元)
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface MutiCurrencyProps {
  amountList?: AmountList[];
}

export const CurrencyAmount = ({ amount = 0, currencySymbol = '' }: AmountList) => {
  return (
    <>
      {(amount ?? 0) < 0 ? '-' : ''}
      {currencySymbol}
      {Math.abs(amount).toFixed(2)}
    </>
  );
};

const MutiCurrency = (props: MutiCurrencyProps) => {
  const { amountList = [] } = props;
  if (!amountList?.length) {
    return <>0</>;
  }
  return (
    <>
      {amountList?.map((amountItem, index) => {
        const { amount = 0, currencySymbol = '' } = amountItem;
        return (
          <span key={amountItem.currencySymbol}>
            <CurrencyAmount amount={amount} currencySymbol={currencySymbol} />
            {amountList.length - 1 !== index ? '; ' : ' '}
          </span>
        );
      })}
    </>
  );
};



export default MutiCurrency;