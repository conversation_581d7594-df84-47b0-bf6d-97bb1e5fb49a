import { uploadImage } from '@/utils/uploadImage';
import { Add, Close } from '@nutui/icons-react-taro';
import { Button, Popup, SafeArea } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';

export interface DeliveryImageUploadProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (imageUrl: string) => void;
  title?: string;
  comfirmText?: string;
}

const DeliveryImageUpload = ({
  visible,
  onClose,
  onConfirm,
  title = '图片(选填)',
  comfirmText = '确认',
}: DeliveryImageUploadProps) => {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [uploading, setUploading] = useState(false);

  // 上传图片
  const handleUpload = async () => {
    setUploading(true);
    try {
      const url = await uploadImage();
      setImageUrl(url);
    } catch (error) {
      console.error('图片上传失败:', error);
      Taro.showToast({ title: '图片上传失败', icon: 'none' });
    } finally {
      setUploading(false);
    }
  };

  // 删除图片
  const handleRemoveImage = () => {
    setImageUrl('');
  };

  // 确认提交
  const handleConfirm = () => {
    onConfirm(imageUrl);
    handleClose();
  };

  // 关闭弹窗
  const handleClose = () => {
    setImageUrl('');
    onClose();
  };

  return (
    <Popup
      visible={visible}
      onClose={handleClose}
      position="bottom"
      closeable
      closeIcon="close"
      title={title}
      round
    >
      <div className="px-[28px] py-[24px] min-h-[400px]">
        {/* 图片上传区域 */}
        <div className="flex justify-center items-center mb-2">
          {imageUrl ? (
            // 已上传图片
            <div className="relative">
              <Image
                src={imageUrl}
                className="w-full h-[300px] rounded-[12px] object-cover"
                mode="aspectFill"
              />
              <div
                className="absolute -top-[10px] -right-[10px] w-[40px] h-[40px] bg-[#F83431] rounded-full flex items-center justify-center"
                onClick={handleRemoveImage}
              >
                <Close size={20} color="#fff" />
              </div>
            </div>
          ) : (
            // 上传按钮
            <div
              className="w-full h-[300px] border-1 border-[#ddd] rounded-[12px] flex flex-col items-center justify-center cursor-pointer"
              onClick={handleUpload}
            >
              {uploading ? (
                <div className="text-[28px] text-[#999]">上传中...</div>
              ) : (
                <>
                  <Add size={30} color="#000000CC" />
                </>
              )}
            </div>
          )}
        </div>
        {/* 底部按钮 */}
        <div className="flex gap-[24px] mt-2">
          <Button
            className="flex-1"
            size="large"
            type="primary"
            onClick={handleConfirm}
          >
            {comfirmText}
          </Button>
        </div>
      </div>
      <SafeArea position="bottom" />
    </Popup>
  );
};

export default DeliveryImageUpload;
