import { Checklist } from '@nutui/icons-react-taro';
import { Divider, SearchBar } from '@nutui/nutui-react-taro';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import React, { useState } from 'react';

export interface ItemProps {
  text: string | any;
  value: string | any;
}

interface ItemsProps {
  label: string;
  keyStr: string;
  item?: ItemProps[];
}

interface CustomSingleChooseProps {
  onClose: () => void;
  onConfirm: (selectedItem: any) => void;
  items: ItemsProps[];
  selected?: any;
  placeholder?: string;
  isSearch?: boolean;
  layout?: 'horizontal' | 'vertical';
  /** 是否显示重置按钮 */
  showReset?: boolean;
  /** 是否允许取消选择（点击已选中项可取消选择） */
  allowDeselect?: boolean;
}

const CustomSingleChoose: React.FC<CustomSingleChooseProps> = ({
  onClose,
  onConfirm,
  items,
  selected,
  placeholder,
  isSearch = false,
  layout = 'vertical',
  showReset = true,
  allowDeselect = true,
}) => {
  const [keyword, setKeyword] = useState<string>('');
  console.log(selected);

  const handleSelect = (keyStr: string, item: string) => {
    onConfirm({
      ...selected,
      [keyStr]: item,
    });
    onClose();
  };


  const handleReset = () => {
    const clearedItems = items.reduce((acc, item) => {
      acc[item.keyStr] = '';
      return acc;
    }, {});
    onConfirm({
      ...selected,
      ...clearedItems
    });
    onClose();
  };


  // 检查某个选项是否被选中
  const isItemSelected = (keyStr: string, value: any) => {
    return selected?.[keyStr] === value;
  };

  return (
    <div className="w-full flex max-h-[60vh] flex-col">
      <div className="flex-1 min-h-0 flex flex-col">
        {isSearch && (
          <SearchBar
            backable={false}
            placeholder={placeholder}
            onChange={setKeyword}
            onClear={() => setKeyword('')}
            style={{
              // @ts-ignore
              '--nutui-searchbar-background': '#FFFFFF',
              '--nutui-searchbar-padding': '0px',
              '--nutui-searchbar-input-height': '36px',
            }}
          />
        )}
        <div className="flex-1 min-h-0 overflow-y-scroll">
          {!isEmpty(items) &&
            items?.map((item, index) => (
              <div key={item.keyStr}>
                {!isEmpty(item?.label) && (
                  <span className="text-[32px] text-black/90 font-medium">{item?.label}</span>
                )}
                <div className="flex flex-wrap mt-[24px]">
                  {item?.item
                    ?.filter((m) => m.text?.toLowerCase().includes(keyword.toLowerCase()))
                    .map((m) => (
                      <div
                        key={m.value}
                        className={
                          layout === 'horizontal'
                            ? "flex basis-1/2 leading-[30px] py-[12px] items-center cursor-pointer"
                            : "flex justify-between w-full leading-[30px] py-[12px] items-center cursor-pointer"
                        }
                        onClick={(e) => {
                          handleSelect(item?.keyStr, m?.value);
                          e.stopPropagation();
                        }}
                      >
                        <div
                          className={classNames({
                            'text-primary': isItemSelected(item.keyStr, m?.value),
                          })}
                        >
                          {m?.text}
                        </div>
                        {isItemSelected(item.keyStr, m?.value) && (
                          <Checklist color="#f49c1f" className="pl-[16px]" />
                        )}
                      </div>
                    ))}
                </div>
                {index + 1 < items?.length && <Divider className="py-[24px]" />}
              </div>
            ))}
        </div>
      </div>
      <div className="flex justify-between gap-[24px] py-[28px]">
        {showReset && (
          <div
            className="flex-1 justify-center text-center items-center py-[14px] rounded-[8px] border border-solid border-[#999999] cursor-pointer"
            onClick={handleReset}
          >
            重置
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomSingleChoose;