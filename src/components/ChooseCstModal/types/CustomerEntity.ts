import { PageRequestParamsType } from '@/types/PageRequestParamsType';

export interface CustomerEntity extends PageRequestParamsType {
  /**
   * 客户地址信息
   */
  addresses?: Address[];
  /**
   * 客户基础信息
   */
  base?: Base;
  /**
   * 客户开票信息
   */
  billings?: Billing[];
  /**
   * 客户联系人信息
   */
  contacts?: Contact[];
  /**
   * 客户开票信息
   */
  images?: Image[];
  /**
   * 客户结算信息
   */
  settle?: Settle;
  /**
   * 客户标签信息
   */
  tags?: Tag[];
}

export interface Address {
  /**
   * 详细地址
   */
  address?: string;
  /**
   * 市编码
   */
  cityCode?: string;
  /**
   * 市名称
   */
  cityName?: string;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 联系人姓名
   */
  firstName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 是否默认地址
   */
  isDefault?: number;
  /**
   * 联系人姓名
   */
  lastName?: string;
  /**
   * 联系人电话
   */
  phone?: string;
  /**
   * 邮编
   */
  postCode?: string;
  /**
   * 区县编码
   */
  prefectureCode?: string;
  /**
   * 区县名称
   */
  prefectureName?: string;
  /**
   * 省编码
   */
  provinceCode?: string;
  /**
   * 省名称
   */
  provinceName?: string;
}

/**
 * 客户基础信息
 */
export interface Base {
  /**
   * abn
   */
  abn?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 客户名称
   */
  cstName?: string;
  /**
   * 客户编码
   */
  cstSn?: string;
  /**
   * 0=启用1=禁用
   */
  cstStatus?: number;
  /**
   * 运费
   */
  deliveryAmount?: number;
  /**
   * 运费类型
   */
  deliveryAmountType?: number;
  /**
   * 财务邮箱
   */
  financeEmail?: string;
  /**
   * 客户ID
   */
  id?: string;
  /**
   * 零售商ID
   */
  memberId?: string;
  /**
   * 零售商名称
   */
  memberName?: string;
  /**
   * 客户简称
   */
  nickName?: string;
  /**
   * 客户备注
   */
  remark?: string;
  /**
   * 业务员ID
   */
  salesmanId?: string;
  /**
   * 业务员名
   */
  salesmanName?: string;
  /**
   * 发送邮件选项
   */
  sendEmailOptions?: number;
  /**
   * 发送财务邮件
   */
  sendFinanceEmailFlag?: boolean;
  /**
   * 客户来源
   */
  source?: number;
  /**
   * 归属门店ID
   */
  storeId?: string;
  /**
   * 归属门店名称
   */
  storeName?: string;
  /**
   * 通用邮箱
   */
  universalEmail?: string;
}

export interface Billing {
  /**
   * 开户行账号
   */
  accountNo?: string;
  /**
   * 地址
   */
  address?: string;
  /**
   * 开户行名称
   */
  bankName?: string;
  /**
   * 开票单位
   */
  billingUnit?: string;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 电话
   */
  phone?: string;
  /**
   * 纳税识别号
   */
  taxNo?: string;
}

export interface Contact {
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 邮箱地址
   */
  email?: string;
  /**
   * 联系人姓名
   */
  firstName?: string;
  /**
   * 是否开通商城权限
   */
  hasMallPermission?: number;
  /**
   * 主键
   */
  id?: string;
  /**
   * 1:默认联系人0:非默认联系人
   */
  isDefault?: number;
  /**
   * 联系人姓名
   */
  lastName?: string;
  /**
   * 联系人电话
   */
  phone?: string;
  /**
   * 职务
   */
  position?: string;
  /**
   * 职务
   */
  positionList?: PositionList[];
  /**
   * QQ号码
   */
  qq?: string;
  /**
   * 客户备注
   */
  remark?: string;
  /**
   * 微信号
   */
  wechat?: string;
}

export interface PositionList {
  /**
   * positionCode
   */
  positionCode?: string;
  /**
   * 职务
   */
  positionName?: string;
}

export interface Image {
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 图片地址
   */
  url?: string;
}

/**
 * 客户结算信息
 */
export interface Settle {
  /**
   * 客户预收（多币种）
   */
  advanceAmountCurrency?: string;
  /**
   * 自定义list对象
   */
  advanceAmountList?: AdvanceAmountList[];
  /**
   * 可用额度
   */
  availableAmount?: number;
  /**
   * 是否挂账
   */
  credit?: boolean;
  /**
   * 信用账期（天）
   */
  creditTerms?: number;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 冻结额度
   */
  freezeAmount?: number;
  /**
   * 客户不收税，0为收税1为不收税
   */
  gstExcluded?: 0 | 1;
  /**
   * 是否多币种1=多币种0=单币种
   */
  isMultiCurrency?: number;
  /**
   * 客户应收（多币种）
   */
  receivableAmountCurrency?: string;
  /**
   * 自定义list对象
   */
  receivableAmountList?: ReceivableAmountList[];
  /**
   * 剩余账期（天）
   */
  remainTerms?: number;
  /**
   * 客户期望额度
   */
  requestCreditLimit?: number;
  /**
   * 客户期望结算方式
   */
  requestSettleType?: string;
  /**
   * 结算类型
   */
  settleType?: string;
  /**
   * 是否可用0=启用1=逾期2=停用
   */
  status?: number;
  /**
   * 状态名
   */
  statusName?: string;
  /**
   * 信用额度
   */
  totalAmount?: number;
  /**
   * 已用额度
   */
  usedAmount?: number;
}

export interface AdvanceAmountList {
  /**
   * 金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface ReceivableAmountList {
  /**
   * 金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface Tag {
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 标签ID
   */
  tagId?: string;
  /**
   * 标签名称
   */
  tagName?: string;
}
