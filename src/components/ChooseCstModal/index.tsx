import CstItem from '@/components/ChooseCstModal/components/CstItem';
import { getCstList } from '@/components/ChooseCstModal/services';
import { CustomerListItem } from '@/components/ChooseCstModal/types/CustomerListItem';
import { Divider, Popup, SafeArea, SearchBar } from '@nutui/nutui-react-taro';
import { ScrollView } from '@tarojs/components';
import { useEffect, useState } from 'react';

export interface ChooseCstModalProps {
  visible: boolean;
  onClose: () => void;
  cstId?: string;
  onConfirm: (record: CustomerListItem) => void;
}

const ChooseCstModal = (props: ChooseCstModalProps) => {
  const { visible, onClose, cstId, onConfirm } = props;
  const [list, setList] = useState<CustomerListItem[]>([]);

  useEffect(() => {
    queryList();
  }, []);

  const queryList = async (keyword?: string) => {
    const cstList = await getCstList({
      keyword,
      needSettle: true,
      needContact: true,
      needTag: true,
      cstStatus: 0,
    });
    setList(cstList);
  };

  const handleSearch = (keyword?: string) => {
    queryList(keyword);
  };

  console.log('list', list);

  return (
    <Popup visible={visible} onClose={onClose} title="选择客户" position="bottom">
      <div>
        <SearchBar placeholder="客户名称" onSearch={handleSearch} onClear={() => queryList()} />
        <ScrollView className="h-[60vh]" scrollY={true}>
          {list?.map((item) => (
            <>
              <CstItem
                record={item}
                onSelect={(value) => {
                  onConfirm(value);
                  onClose();
                }}
                cstId={cstId}
              />
              <div className="mx-[28px]">
                <Divider />
              </div>
            </>
          ))}
        </ScrollView>
        <SafeArea position={'bottom'} />
      </div>
    </Popup>
  );
};

export default ChooseCstModal;
