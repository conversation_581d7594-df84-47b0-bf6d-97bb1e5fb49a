import { PageResponseDataType } from '../../../types/PageResponseDataType';
import { MemberAccountEntity } from './types/MemberAccountEntity';

import { AdvanceAccountEntity } from '@/components/AccountSelectModal/types/AdvanceAccountEntity';
import { QueryAdvanceAccountRequest } from '@/components/AccountSelectModal/types/query.advance.account.request';
import { post } from '@/utils/request';

/**
 * 查询门店账户列表
 * @param params
 * @returns
 */
export const queryMemberAccountPage = (params: {
  belongToStore?: string[];
  memberAccountName?: string;
}) => {
  return post<PageResponseDataType<MemberAccountEntity>>(`/ipmsaccount/memberAccount/queryByPage`, {
    data: params,
  });
};

/**
 * 查询客户预收账户
 */
export const queryAdvanceAccount = (data: QueryAdvanceAccountRequest) => {
  return post<AdvanceAccountEntity>(`/ipmsaccount/queryAdvanceAccount`, { data });
};
