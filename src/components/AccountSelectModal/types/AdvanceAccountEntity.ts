export interface AdvanceAccountEntity {
  /**
   * 所属门店
   */
  accounExtends?: AccounExtend[];
  /**
   * 账户角色@seecom.ipms.finance.account.api.dto.enums.AccountRoleEnums
   */
  accountRole?: number;
  /**
   * 账户类型@seecom.ipms.finance.account.api.dto.enums.AccountTypeEnums
   */
  accountType?: number;
  /**
   * 可用额度，单位：分
   */
  availableAmount?: number;
  /**
   * 可用额度，单位：元
   */
  availableAmountYuan?: number;
  createPerson?: string;
  createTime?: string;
  /**
   * 账期起始日期
   */
  creditDate?: string;
  /**
   * 信用账期
   */
  creditTerms?: number;
  /**
   * 账期类型@seecom.ipms.finance.account.api.dto.enums.CreditTermsTypeEnums
   */
  creditTermsType?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
  /**
   * 客户ID
   */
  customerId?: string;
  /**
   * 客户名称
   */
  customerName?: string;
  /**
   * 冻结额度，单位：分
   */
  freezeAmount?: number;
  /**
   * 冻结额度，单位：元
   */
  freezeAmountYuan?: number;
  /**
   * id
   */
  id?: string;
  isDelete?: number;
  /**
   * 是否已被首次使用
   */
  isFirstUse?: number;
  /**
   * 零售商账户账户
   */
  memberAccountName?: string;
  memberId?: string;
  /**
   * 零售商名称
   */
  memberName?: string;
  /**
   * 剩余账期
   */
  remainTerms?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 是否可用@seecom.ipms.finance.account.api.dto.enums.AccountStatusEnums
   */
  status?: number;
  /**
   * 总额度，单位：分
   */
  totalAmount?: number;
  /**
   * 总额度，单位：元
   */
  totalAmountYuan?: number;
  /**
   * 三方账号，非挂账账户对应的支付宝微信账号，银行卡号，账户类型为现金默认：CASH
   */
  tripartiteAccount?: string;
  /**
   * 三方账号描述，银行账号填写银行名称
   */
  tripartiteAccountDesc?: string;
  updatePerson?: string;
  updateTime?: string;
  /**
   * 已用额度，单位：分
   */
  usedAmount?: number;
  /**
   * 已用额度，单位：元
   */
  usedAmountYuan?: number;
}

export interface AccounExtend {
  /**
   * 所属门店ID
   */
  belongToStoreId?: string;
  /**
   * 所属门店名称
   */
  belongToStoreName?: string;
}
