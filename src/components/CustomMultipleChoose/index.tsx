import { Checklist } from '@nutui/icons-react-taro';
import { Divider, SearchBar } from '@nutui/nutui-react-taro';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import React, { useState } from 'react';

export interface ItemProps {
  text: string | any;
  value: string | any;
}

interface ItemsProps {
  label: string;
  keyStr: string;
  multiple?: boolean;
  item?: ItemProps[];
}

interface CustomMultipleChooseProps {
  onClose: () => void;
  onConfirm: (selectedItems: any) => void;
  items: ItemsProps[];
  selected?: any;
  placeholder?: string;
  isSearch?: boolean;
  layout?: 'horizontal' | 'vertical';
}

const CustomMultipleChoose: React.FC<CustomMultipleChooseProps> = ({
  onClose,
  onConfirm,
  items,
  selected,
  placeholder,
  isSearch = false,
  layout = 'horizontal',
}) => {
  const [keyword, setKeyword] = useState<string>('');

  const [selectedItems, setSelectedItems] = useState(selected ?? {});
  const handleSelect = (keyStr: string, multiple: boolean, item: string) => {
    console.log(keyStr, multiple, item);

    setSelectedItems((prevSelectedItems) => {
      const prevItems = prevSelectedItems[keyStr] || [] || '';
      let newItems;
      if (prevItems.includes(item) && multiple) {
        newItems = prevItems.filter((i) => i !== item);
      } else if (!multiple && prevItems == item) {
        newItems = '';
      } else {
        if (!multiple) {
          //单选直接替换
          newItems = item;
        } else {
          newItems = [...prevItems, item];
        }
      }
      return {
        ...prevSelectedItems,
        [keyStr]: newItems,
      };
    });
  };

  const handleConfirm = () => {
    onConfirm(selectedItems);
    onClose();
  };
  const handleRest = () => {
    setSelectedItems(clearSelectedItems(selectedItems));
    onConfirm(clearSelectedItems(selectedItems));
    onClose();
  };
  const clearSelectedItems = (obj) => {
    const clearedItems = Object.keys(obj).reduce((acc, key) => {
      if (Array.isArray(obj[key])) {
        acc[key] = [];
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        acc[key] = clearSelectedItems(obj[key]);
      } else {
        acc[key] = '';
      }
      return acc;
    }, {});
    return clearedItems;
  };

  return (
    <div className="w-full flex max-h-[60vh] flex-col">
      <div className="flex-1 min-h-0 flex flex-col">
        {isSearch && (
          <SearchBar
            backable={false}
            placeholder={placeholder}
            onChange={setKeyword}
            onClear={() => setKeyword('')}
            style={{
              // @ts-ignore
              '--nutui-searchbar-background': '#FFFFFF',
              '--nutui-searchbar-padding': '0px',
              '--nutui-searchbar-input-height': '36px',
            }}
          />
        )}
        <div className="flex-1 min-h-0 overflow-y-scroll">
          {!isEmpty(items) &&
            items?.map((item, index) => (
              <div key={item.keyStr}>
                {!isEmpty(item?.label) && (
                  <span className="text-[32px] text-black/90 font-medium">{item?.label}</span>
                )}
                <div className="flex flex-wrap mt-[24px]">
                  {item?.item
                    ?.filter((m) => m.text?.toLowerCase().includes(keyword.toLowerCase()))
                    .map((m) => (
                      <div
                        className={
                          layout === 'horizontal'
                            ? "flex basis-1/2 leading-[30px] py-[12px] items-center"
                            : "flex justify-between w-full leading-[30px] py-[12px] items-center"
                        }
                        onClick={(e) => {
                          handleSelect(item?.keyStr, item?.multiple!, m?.value);
                          e.stopPropagation();
                        }}
                      >
                        <div
                          className={classNames({
                            'text-primary': selectedItems?.[item.keyStr]?.includes(m?.value),
                          })}
                        >
                          {m?.text}
                        </div>
                        {selectedItems?.[item.keyStr]?.includes(m?.value) && (
                          <Checklist color="#f49c1f" className="pl-[16px]" />
                        )}
                      </div>
                    ))}
                </div>
                {index + 1 < items?.length && <Divider className="py-[24px]" />}
              </div>
            ))}
        </div>
      </div>
      <div className="flex justify-between gap-[24px] py-[28px]">
        <div
          className="flex-1 justify-center text-center items-center py-[14px] rounded-[8px] border border-solid border-[#999999]"
          onClick={handleRest}
        >
          重置
        </div>
        <div
          className="flex-1 justify-center text-white text-center items-center py-[14px] rounded-[8px] bg-[#F49C1F]"
          onClick={handleConfirm}
        >
          确定
        </div>
      </div>
    </div>
  );
};

export default CustomMultipleChoose;
