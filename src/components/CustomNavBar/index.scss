.custom-header,
.custom-header-general {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  box-sizing: content-box;
  width: 100%;
  padding-top: env(safe-area-inset-top);

  .title {
    align-items: center;
    padding-left: 30px;
    color: #111111;
    font-weight: bold;
    font-size: 34px;
  }
}

.custom-header {
  background-image: url('../../assets/bgTop.png');
  background-repeat: no-repeat;
  background-size: cover;
}

page,
html {
  background: url('../../assets/bgTop.png') #f5f5f5 no-repeat fixed;
  background-size: contain;
}

.custom-header .nut-navbar-left {
  height: 92%;
  background-image: url('../../assets/bgTop.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-attachment: fixed;
}

.custom-header .nut-navbar-right {
  height: 92%;
  background-image: url('../../assets/bgTop.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-attachment: fixed;
}
