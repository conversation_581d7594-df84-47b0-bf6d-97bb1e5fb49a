import { ArrowLeft } from '@nutui/icons-react-taro';
import { NavBar } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from "@tarojs/taro";
import './index.scss';

const CustomNavBar = ({ title, showBack = true, color = 'primary' }: {
  title: string;
  showBack?: boolean;
  color?: 'primary' | 'general';
}) => {
  const statusBarHeight = 0;
  const navBarHeight = statusBarHeight + 44;

  return (
    <>
      <View className={color === 'primary' ? "custom-header" : "custom-header-general"} style={{ height: `${navBarHeight}px` }}>
        <NavBar
          style={{ marginTop: `${statusBarHeight}px`, backgroundColor: 'transparent' }}
          back={
            showBack && (
              <span className="w-[70px] flex">
                <ArrowLeft size={18} />
              </span>
            )
          }
          onBackClick={() => {
            Taro.navigateBack();
          }}
          titleAlign={showBack ? 'center' : 'left'}
        >
          <span>{title}</span>
        </NavBar>
      </View>
      <div style={{ height: `${navBarHeight}px` }} />
    </>
  );
};

export default CustomNavBar;
