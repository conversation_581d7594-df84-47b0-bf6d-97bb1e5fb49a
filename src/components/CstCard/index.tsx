import ChooseCstModal from '@/components/ChooseCstModal';
import { getCstDetail } from '@/components/ChooseCstModal/services';
import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import { CustomerListItem } from '@/components/ChooseCstModal/types/CustomerListItem';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Tag } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import defaultIcon from './imgs/default.svg';

export interface CstCardProps {
  className?: string;
  cstId?: string;
  onConfirm: (data: CustomerEntity) => void;
  disabled?: boolean;
  /** 是否财务收款 */
  isFin?: boolean;
}

const CstCard = (props: CstCardProps) => {
  const { className, onConfirm, cstId, disabled } = props;
  const [visible, setVisible] = useState(false);
  const [currentCst, setCurrentCst] = useState<CustomerEntity>();

  useEffect(() => {
    if (cstId && disabled) {
      queryCstDetail();
    }
  }, [cstId]);

  const queryCstDetail = (selectedCst?: CustomerListItem) => {
    if (selectedCst?.cstId ?? cstId) {
      // @ts-ignore
      getCstDetail({ cstId: selectedCst?.cstId ?? cstId }).then((result) => {
        setCurrentCst(result);
        onConfirm?.(result);
      });
    }
  };

  return (
    <div className="relative">
      <div
        className={`bg-white p-[24px] rounded-[16px] flex ${className ?? ''}`}
        onClick={() => setVisible(true)}
      >
        <Image
          src={currentCst?.images?.[0]?.url ?? defaultIcon}
          style={{ width: '50px', height: '50px' }}
          mode={'aspectFit'}
        />
        <div className="flex-1 ml-[24px]">
          {currentCst ? (
            <div>
              <div>{currentCst?.base?.cstName}</div>
              <div className="flex gap-[12px] flex-wrap my-1.5">
                {currentCst?.settle?.settleType && (
                  <Tag type="primary">{currentCst?.settle?.settleType}</Tag>
                )}
                {currentCst.tags?.map((item) => (
                  <Tag type="info">{item.tagName}</Tag>
                ))}
              </div>
              <div className="mt-[12px] flex flex-wrap gap-1.5 border-top-[2px] border-solid border-gray-100 pt-1.5">
                <div className="text-[26px] text-thirdary">
                  归属门店: {currentCst.base?.storeName};
                </div>
                <div className="text-[26px] text-thirdary">
                  Suburb: {currentCst.addresses?.[0]?.prefectureName}
                </div>
                {currentCst?.settle?.credit && (
                  <div className="text-[26px] text-thirdary">
                    信用额度:
                    {currentCst.settle?.totalAmount ?? '-'}/可用
                    {currentCst.settle?.availableAmount ?? '-'}
                  </div>
                )}
                <div className="text-[26px] text-thirdary">
                  客户应收:
                  {currentCst.settle?.receivableAmountCurrency ?? '-'}
                </div>
                <div className="text-[26px] text-thirdary">
                  客户预收:
                  {currentCst.settle?.advanceAmountCurrency ?? '-'}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-thirdary h-full flex items-center">请先选择客户</div>
          )}
        </div>
        {!disabled && (
          <div
            className={classNames('flex items-center', {
              'opacity-[0.5]': !currentCst,
            })}
          >
            <ArrowRight />
          </div>
        )}
      </div>
      <ChooseCstModal
        visible={visible}
        onClose={() => setVisible(false)}
        onConfirm={(selectedCst) => queryCstDetail(selectedCst)}
        cstId={cstId}
      />
      {disabled && <div className="absolute inset-0 bg-white opacity-0" />}
    </div>
  );
};

export default CstCard;
