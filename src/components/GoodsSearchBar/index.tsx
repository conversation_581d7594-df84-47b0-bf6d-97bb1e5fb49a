import scan from '@/assets/scan.svg';
import { scanCode } from '@/utils/scanCode';
import { Add, IconFont } from '@nutui/icons-react-taro';
import { Divider, SearchBar } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useState } from 'react';
interface GoodsSearchBarProps {
  scanShow?: boolean;
  isShowScan?: boolean;
  keyStr: string;
  inputValue: (e) => void;
  placeholder?: string;
  addUrl?: string;
}
const GoodsSearchBar: React.FC<GoodsSearchBarProps> = ({
  inputValue,
  keyStr,
  isShowScan = true,
  placeholder = '商品名称/商品编码/OE/供应商编码',
  addUrl,
}) => {
  const handleChange = (value: string) => {
    inputValue({ [keyStr]: value });
  };

  const [value, setValue] = useState<string>();

  return (
    <SearchBar
      placeholder={placeholder}
      backable={false}
      value={value}
      onSearch={handleChange}
      onClear={() => handleChange('')}
      rightIn={
        isShowScan && (
          <div
            className="flex items-center pr-[16px]"
            onClick={() => {
              scanCode().then((result) => {
                if (result) {
                  setValue(result);
                  handleChange(result);
                }
              });
            }}
          >
            <Divider direction="vertical" />
            <IconFont name={scan} style={{ width: '31px' }} />
          </div>
        )
      }
      right={addUrl && <Add onClick={() => Taro.navigateTo({ url: addUrl })} />}
    />
  );
};

export default GoodsSearchBar;
