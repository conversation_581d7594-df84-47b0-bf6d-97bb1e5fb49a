// 返回 日/月/年

import moment from 'dayjs';

const getDateFormat = (date: string | number | Date, format: string) => {
  return moment(date).format(format);
};

export const TimeFormat = (props: { time: string | number | Date, showTime?: boolean }) => {
  const { time } = props;
  if (!time) return <span>-</span>;
  return <span>{getDateFormat(time, props.showTime ? 'DD/MM/YYYY HH:mm:ss' : 'DD/MM/YYYY')}</span>;
};


export const TimeRangeFormat = (props: { startTime: string | number | Date, endTime: string | number | Date }) => {
  const { startTime, endTime } = props;
  if (!startTime || !endTime) return <span>-</span>;
  return <span>{getDateFormat(startTime, 'DD/MM/YYYY')} - {getDateFormat(endTime, 'DD/MM/YYYY')}</span>;
};