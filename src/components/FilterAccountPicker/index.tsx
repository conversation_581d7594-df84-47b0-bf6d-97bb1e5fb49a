import { accountListQuerySimple } from "@/services/common";
import { Checklist } from '@nutui/icons-react-taro';
import { SearchBar } from '@nutui/nutui-react-taro';
import { useEffect, useState } from 'react';

export interface FilterAccountPickerProps {
  accountId?: string;
  onChange: (accountId?: string) => void;
  label: string;
}

const FilterAccountPicker = (props: FilterAccountPickerProps) => {
  const { accountId, onChange, label } = props;
  const [keyword, setKeyword] = useState<string>('');
  const [list, setList] = useState<{
    id: string;
    name: string;
  }[]>([]);

  const filteredItems = list.filter((item) => item.name.includes(keyword));

  useEffect(() => {
    accountListQuerySimple({}).then((result) => {
      setList(result ?? []);
    });
  }, []);

  return (
    <div className="h-[50vh] flex w-full flex-col">
      <SearchBar
        placeholder={
          label
        }
        className="!p-0 mb-[24px]"
        value={keyword}
        onSearch={setKeyword}
        onClear={() => setKeyword('')}
      />
      <div className="flex-1 min-h-0 overflow-y-scroll">
        {
          filteredItems.map((item) => (
            <div
              className="flex justify-between leading-[48px] text-[28px] px-[28px]"
              onClick={() => {
                onChange(accountId === item.id ? '' : item.id);
              }}
            >
              <span className="">
                {item.name}
              </span>
              {accountId === item.id && (
                <span className="flex-shrink-0">
                  <Checklist color="#f49c1f" />
                </span>
              )}
            </div>
          ))
        }
      </div>
    </div>
  );
};

export default FilterAccountPicker;
