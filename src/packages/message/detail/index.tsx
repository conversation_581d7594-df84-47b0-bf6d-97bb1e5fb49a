import CustomNavBar from '@/components/CustomNavBar';
import { queryMsgDetail, setRead } from '@/packages/message/services';
import { MsgStatus } from '@/packages/message/types/MsgStatus';
import { MsgListItemEntity } from '@/packages/message/types/msg.list.item.entity';
import { useLoad, useRouter } from '@tarojs/taro';
import { useState } from 'react';
// @ts-ignore
// import '@/assets/lightPage.scss';
import './index.scss';

export default function Index() {
  const router = useRouter();
  const { msgId } = router.params;

  const [msg, setMsg] = useState<MsgListItemEntity | undefined>(null);

  useLoad(async () => {
    if (!msgId) {
      return;
    }
    queryMsgDetail(Number(msgId)).then((msgDetail) => {
      setMsg(msgDetail);
      if (msgDetail?.msgStatus === MsgStatus.NoRead) {
        setRead([msgDetail.id!]);
      }
    });
  });

  return (
    <div className="flex flex-col h-screen">
      <CustomNavBar title="消息详情" color='general' />
      <div className="px-[60px] text-block opacity-90" style={{ lineHeight: 1.5 }}>
        <div className="text-[40px] mt-[16px]">
          <div>{msg?.title ?? ''}</div>
        </div>
        <div className="text-[26px] mt-[16px] text-[hsla(0, 0%, 0%, 0.45)]">
          <div>{msg?.createTime ?? ''}</div>
        </div>
        <div className="text-[32px] mt-[32px]">
          <div dangerouslySetInnerHTML={{ __html: msg?.content ?? '' }}></div>
        </div>
      </div>
    </div>
  );
}
