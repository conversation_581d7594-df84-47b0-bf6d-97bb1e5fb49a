import '@/assets/lightPage.scss';
import Card from '@/components/Card';
import CustomNavBar from '@/components/CustomNavBar';
import FunPagination from '@/components/Pagination/FunPagination';
import { queryMsgList } from '@/packages/message/services';
import { MsgStatus } from '@/packages/message/types/MsgStatus';
import { MsgListItemEntity } from '@/packages/message/types/msg.list.item.entity';
import { QueryPostListRequest } from '@/packages/stocks/output/list/types/query.post.list.request';
import RegUtils from '@/utils/RegUtils';
import { IconFont } from '@nutui/icons-react-taro';
import { SafeArea } from '@nutui/nutui-react-taro';
import { navigateTo } from '@tarojs/taro';
import Point from './images/point.svg';
import './index.scss';

export default function Index() {
  const params = { pageSize: 10, isDelete: 0 };

  /**
   * 列表查询
   * @param page
   */
  const fetchData = (param?: QueryPostListRequest): Promise<MsgListItemEntity[]> => {
    return queryMsgList(param).then((result) => result?.data ?? []);
  };

  return (
    <div className="flex flex-col h-screen">
      <CustomNavBar title="消息" />
      <div className="flex-1 min-h-0 overflow-scroll mt-[0px]">
        <FunPagination
          fetchData={fetchData}
          params={params}
          renderItem={(record: MsgListItemEntity) => (
            <Card
              title={
                <div className="flex flex-grow items-center">
                  {record.msgStatus == MsgStatus.NoRead && (
                    <IconFont name={Point} className="ml-[-12px]" />
                  )}
                  {record.title ?? ''}
                </div>
              }
              onClick={() =>
                navigateTo({ url: '/packages/message/detail/index?msgId=' + record.id })
              }
            >
              <div style={{ lineHeight: '1.5' }}>
                <div className='multi-line-ellipsis mt-[14px] text-[28px] text-[rgba(0,0,0,0.6)]'>
                  {RegUtils.removeHtmlTag(record?.content ?? '')}
                </div>
              </div>
              <div className="mt-[24px] text-[26px] text-[rgba(0,0,0,0.6)]">
                {record.createTime}
              </div>
            </Card>
          )}
        />
      </div>
      <SafeArea position={'bottom'} />
    </div>
  );
}
