import '@/assets/lightPage.scss';
import ChooseCategoryCard from '@/components/ChooseCategoryCard';
import CustomMultipleChoose from '@/components/CustomMultipleChoose';
import CustomNavBar from '@/components/CustomNavBar';
import GoodsSearchBar from '@/components/GoodsSearchBar';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import GoodItem from '@/packages/sales/order/chooseGoodsPage/components/GoodItem';
import { queryStoreGoods } from '@/packages/sales/order/chooseGoodsPage/services';
import { QueryStoreGoodsPageRequest } from '@/packages/sales/order/chooseGoodsPage/types/query.store.goods.page.request';
import { StoreGoodsEntity } from '@/packages/sales/order/chooseGoodsPage/types/store.goods.entity';
import useOrderSaleStore from '@/packages/sales/order/edit/store';
import { getOrderByOrderNoForDbReturnSelected } from '@/packages/sales/order/list/services';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { queryGoodsPropertyPage } from '@/packages/stocks/inventory/services';
import { EnableType } from '@/types/EnableType';
import { IconFont } from '@nutui/icons-react-taro';
import { Badge, Button, Menu, Price, SafeArea } from '@nutui/nutui-react-taro';
import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import _ from 'lodash';
import { useEffect, useRef, useState } from 'react';
import packageIcon from './imgs/package.svg';

export interface ChooseItem extends StoreGoodsEntity {
  price: number;
  number: number;
  id?: string;
}

const inventoryPanelItems = [
  {
    keyStr: 'inventory',
    multiple: true,
    label: '',
    item: [
      {
        text: '本地有货',
        value: 'hasLocalStock',
      },
      {
        text: '仅看有货',
        value: 'hasStock',
      },
    ],
  },
];

const ChooseGoods = () => {
  // 已选的商品（此处给未生成订单时使用）
  const [chooseItems, setChooseItems] = useState<ChooseItem[]>([]);
  // 订单详情（此处给生成订单后使用）
  const [orderDetail, setOrderDetail] = useState<OrderListItemEntity>();
  const [params, setParams] = useState<QueryStoreGoodsPageRequest>({
    pageSize: 10,
    isFetchWarehouseCostPrice: true,
    isFetchLastSalePrice: true,
    itemStatus: EnableType.ENABLE,
  });
  const [brandItems, setBrandItems] = useState<any>([]);
  const inventoryMenuRef = useRef<any>();
  const brandMenuRef = useRef<any>();
  const categoryMenuRef = useRef<any>();

  // 真机上面底部的按钮层会挡住商品详情的底部栏，这里使用显隐操作避免遮挡
  const [footerBarVisible, setFooterBarVisible] = useState(true);

  const router = useRouter();
  const { source } = router.params;
  const isFromVIN = source === 'VIN';

  const orderSaleStore = useOrderSaleStore();

  const {
    baseInfo: { orderNo, warehouseId, storeId, cstId },
    modelId,
  } = orderSaleStore;

  useEffect(() => {
    queryGoodsPropertyPage({ pageNo: 1, pageSize: 1000, brandStatus: '1' }, 'brand').then(
      (result) => {
        setBrandItems([
          {
            keyStr: 'brandIdList',
            multiple: true,
            label: '',
            item: result.data?.map((s) => ({ text: s.brandName, value: s.brandId })),
          },
        ]);
      },
    );
  }, []);

  /**
   * 组装查询参数
   */
  useEffect(() => {
    const extraParams: QueryStoreGoodsPageRequest = {
      cstId,
      storeId,
    };
    if (warehouseId) {
      extraParams.warehouseId = warehouseId;
      extraParams.hasStock = true;
      extraParams.isFetchLocalInventory = true;
      extraParams.isFetchLocation = true;
    }
    if (modelId) {
      extraParams.modelId = modelId;
    }
    if (Object.keys(extraParams).length) {
      setParams({ ...params, ...extraParams });
    }
  }, [warehouseId, modelId, cstId, storeId]);

  useEffect(() => {
    queryDetail();
  }, [orderNo]);

  useDidShow(() => {
    queryDetail();
  });

  /**
   * 查询订单详情
   */
  const queryDetail = () => {
    if (orderNo) {
      getOrderByOrderNoForDbReturnSelected(orderNo).then((result) => {
        if (result) {
          setOrderDetail(result);
        }
      });
    }
  };

  /**
   * 列表查询
   * @param page
   */
  const fetchData = (query): Promise<StoreGoodsEntity[]> => {
    if (!params.warehouseId) {
      return new Promise((resolve) => {
        resolve([]);
      });
    }
    return queryStoreGoods({ ...params, ...query }).then((result) => {
      const data = result?.data ?? [];
      return data;
    });
  };

  /**
   * 单个商品更新价格和数量
   */
  const handleItemConfirm = (data: ChooseItem) => {
    if (!orderNo) {
      if (!chooseItems.find((item) => item.itemId === data.itemId)) {
        setChooseItems([...chooseItems, { ...data }]);
      } else {
        setChooseItems(
          chooseItems.map((item) => (item.itemId === data.itemId ? { ...item, ...data } : item)),
        );
      }
    }
  };

  /**
   * 计算总价
   */
  const getTotalPrice = () => {
    let totalPrice = 0;
    if (orderNo) {
      orderDetail?.orderGoodsROList?.forEach((item) => {
        totalPrice = _.add(totalPrice, _.multiply(item.unitPriceYuan!, item.saleNum!));
      });
    } else {
      chooseItems.forEach((item) => {
        totalPrice = _.add(totalPrice, _.multiply(item.price, item.number));
      });
    }
    return _.round(totalPrice, 2);
  };

  /**
   * 计算总数
   */
  const getTotalNumber = () => {
    let totalNumber = 0;
    if (orderNo) {
      orderDetail?.orderGoodsROList?.forEach((item) => {
        totalNumber += item.saleNum! * 1;
      });
    } else {
      chooseItems.forEach((item) => {
        totalNumber += item.number * 1;
      });
    }
    return totalNumber;
  };

  /**
   * 提交数据
   */
  const handleSubmit = () => {
    console.log('chooseGoodsPage => handleSubmit');
    if (
      (!orderNo && chooseItems.length === 0) ||
      (orderNo && orderDetail?.orderGoodsROList?.length === 0 && chooseItems.length === 0)
    ) {
      Taro.showToast({ title: '请至少选择一条商品', icon: 'none' });
      return;
    }
    console.log('chooseGoodsPage => handleSubmit', chooseItems);
    orderSaleStore.setChooseItems(chooseItems);
    Taro.navigateBack({ delta: isFromVIN ? 2 : 1 });
  };

  const getOptionValue = () => {
    const value: number[] = [];
    if (params.hasStock) {
      value.push(1);
    }
    if (params.isAccurate) {
      value.push(2);
    }
    return value;
  };

  return (
    <div className="flex flex-col h-screen">
      <CustomNavBar title="商品" showBack={true} />
      <div className="mb-[24px]">
        <div className="mb-[18px]">
          <GoodsSearchBar
            inputValue={(query) => {
              setParams({ ...params, ...query });
            }}
            keyStr="queryKeyWord"
          />
        </div>
        <MenuWrap
          checkbox={{
            options: [
              {
                label: '精确查询',
                value: 2,
              },
            ],
            value: getOptionValue(),
            onChange: (value) => {
              setParams({ ...params, hasStock: value.includes(1), isAccurate: value.includes(2) });
            },
          }}
          menu={
            <Menu>
              <Menu.Item title="库存" ref={inventoryMenuRef}>
                <CustomMultipleChoose
                  key={'barnItems'}
                  onClose={() => {
                    inventoryMenuRef.current?.toggle(false);
                  }}
                  onConfirm={(e) => {
                    const inventoryParams = {
                      hasLocalStock: e.inventory.includes('hasLocalStock'),
                      hasStock: e.inventory.includes('hasStock'),
                    };
                    setParams({ ...params, ...inventoryParams });
                  }}
                  items={inventoryPanelItems}
                />
              </Menu.Item>
              <Menu.Item title="品牌" ref={brandMenuRef}>
                <CustomMultipleChoose
                  isSearch={true}
                  key={'barnItems'}
                  onClose={() => {
                    brandMenuRef.current?.toggle(false);
                  }}
                  onConfirm={(e) => {
                    setParams({ ...params, ...e });
                  }}
                  items={brandItems}
                />
              </Menu.Item>
              <Menu.Item title="品类" ref={categoryMenuRef}>
                <ChooseCategoryCard
                  values={params.categoryIdList ?? []}
                  onChange={(v, items) => {
                    setParams({ ...params, categoryIdList: v, categoryIdAndNameList: items });
                    categoryMenuRef.current?.toggle(false);
                  }}
                />
              </Menu.Item>
            </Menu>
          }
        />
      </div>
      <div className="flex-1 min-h-0">
        {warehouseId && (
          <FunPagination
            fetchData={fetchData}
            params={params}
            renderItem={(record, index) => {
              if (orderNo) {
                const detailGood = orderDetail?.orderGoodsROList?.find(
                  (item) => item.itemId === record.itemId,
                );
                return (
                  <GoodItem
                    record={record}
                    price={detailGood?.unitPriceYuan}
                    number={detailGood?.saleNum}
                    id={detailGood?.id}
                    onConfirm={handleItemConfirm}
                    cstId={cstId}
                    storeId={storeId}
                    warehouseId={warehouseId}
                    orderId={orderDetail?.orderId}
                    onRefresh={queryDetail}
                    index={index}
                    setFooterBarVisible={setFooterBarVisible}
                  />
                );
              } else {
                const currentItem = chooseItems.find((item) => item.itemId === record.itemId);
                return (
                  <GoodItem
                    record={record}
                    price={currentItem?.price}
                    number={currentItem?.number}
                    onConfirm={handleItemConfirm}
                    cstId={cstId}
                    storeId={storeId}
                    warehouseId={warehouseId}
                    index={index}
                    setFooterBarVisible={setFooterBarVisible}
                  />
                );
              }
            }}
          />
        )}
      </div>
      {footerBarVisible && (
        <div className="bg-white p-[28px]">
          <div className="flex justify-between ">
            <div
              className="flex"
              onClick={() => {
                if (!orderSaleStore.baseInfo.orderNo) {
                  Taro.showToast({ title: '请先点击选好了按钮生成订单', icon: 'none' });
                } else {
                  Taro.navigateTo({
                    url: `/packages/sales/order/salesGoodsPage/index?orderNo=${orderDetail?.orders?.orderNo}`,
                  });
                }
              }}
            >
              <Badge value={getTotalNumber()}>
                <IconFont name={packageIcon} style={{ height: '30px' }} />
              </Badge>
              <div className="ml-2">
                <Price price={getTotalPrice()} />
                <div className="text-[24px] text-secondary">GST Excl</div>
              </div>
            </div>
            <Button type="primary" onClick={handleSubmit}>
              选好了
            </Button>
          </div>
          <SafeArea position="bottom" />
        </div>
      )}
    </div>
  );
};

export default ChooseGoods;
