import Card from '@/components/Card';
import CustomNavBar from '@/components/CustomNavBar';
import DetailList from '@/components/DetailList';
import { queryPurchaseRecord } from '@/packages/sales/order/purchaseHistoryPage/services';
import { PurchaseRecordEntity } from '@/packages/sales/order/purchaseHistoryPage/types/purchase.record.entity';
import { Price } from '@nutui/nutui-react-taro';
import { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';

const PurchaseHistoryPage = () => {
  const router = useRouter();
  const [detail, setDetail] = useState<PurchaseRecordEntity[]>();

  console.log('router.params', router.params);
  const { itemId } = router.params;

  useEffect(() => {
    if (itemId) {
      queryPurchaseRecord(itemId).then((result) => {
        if (result) {
          setDetail(result);
        }
      });
    }
  }, [itemId]);

  return (
    <>
      <CustomNavBar title="采购历史" />
      <Card title="近10次采购历史">
        <DetailList
          dataSource={detail?.map((item) => ({
            label: (
              <div className="my-[10px]">
                <div className="text-gray-900">{item.supplierName}</div>
                <div className="text-gray-900">{item.orderNo}</div>
                <div className="text-thirdary text-[28px]">{item.orderTime}</div>
              </div>
            ),
            value: (
              <div className="flex items-end flex-col">
                <div>&ensp;</div>
                <Price className="!text-gray-900" size="normal" price={item.sumAmount} />
                <div className="text-thirdary text-[28px]">x{item.sumQuantity}</div>
              </div>
            ),
          }))}
          justified={true}
          divider={true}
        />
      </Card>
    </>
  );
};

export default PurchaseHistoryPage;
