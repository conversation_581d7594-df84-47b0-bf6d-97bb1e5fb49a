export enum DiscountType {
  NONE,
  DISCOUNT_ON_ORDER,
  DEDUCTION_ON_ORDER,
  COUPON,
}

export enum DiscountTypeName {
  NONE = '无',
  DISCOUNT_ON_ORDER = '整单折',
  DEDUCTION_ON_ORDER = '整单减',
  COUPON = '优惠券',
}

export const discountTypeOptions = [
  {
    label: DiscountTypeName.NONE,
    value: DiscountType.NONE,
  },
  {
    label: DiscountTypeName.DISCOUNT_ON_ORDER,
    value: DiscountType.DISCOUNT_ON_ORDER,
  },
  {
    label: DiscountTypeName.DEDUCTION_ON_ORDER,
    value: DiscountType.DEDUCTION_ON_ORDER,
  },
  {
    label: DiscountTypeName.COUPON,
    value: DiscountType.COUPON,
  },
];
