import { PayChannel } from '@/packages/sales/order/edit/components/Settle/types/PayChannel';
import { PayKind } from '@/packages/sales/order/edit/components/Settle/types/PayKind';
import { DeliveryMethod } from '@/packages/sales/order/edit/types/DeliveryMethod';
import { OrderChannelCode } from '@/packages/sales/order/edit/types/order.channel.code';

export interface CreateDraftOrderRequest {
  /**
   * None
   */
  draftOrderAddress?: DraftOrderAddress;
  /**
   * None
   */
  draftOrderDelivery?: DraftOrderDelivery;
  /**
   * 商品明细信息
   */
  draftOrderItemList?: DraftOrderItemList[];
  /**
   * None
   */
  draftOrderMain?: DraftOrderMain;
  /**
   * None
   */
  draftOrderPay?: DraftOrderPay;
  /**
   * 零售商id
   */
  memberId?: string;
  /**
   * 零售商名称
   */
  memberName?: string;
  /**
   * 操作人姓名
   */
  operator?: string;
  /**
   * 操作人工号
   */
  operatorId?: string;
}

/**
 * None
 */
export interface DraftOrderAddress {
  /**
   * 客户物流地址
   */
  address?: string;
  /**
   * 地址id
   */
  addressId?: string;
  /**
   * 市编码
   */
  cityCode?: string;
  /**
   * 市名称
   */
  cityName?: string;
  /**
   * 客户联系人
   */
  name?: string;
  /**
   * 客户联系人手机号码
   */
  phone?: string;
  /**
   * 区编码
   */
  prefectureCode?: string;
  /**
   * 区名称
   */
  prefectureName?: string;
  /**
   * 省编码
   */
  provinceCode?: string;
  /**
   * 省名称
   */
  provinceName?: string;
}

/**
 * None
 */
export interface DraftOrderDelivery {
  /**
   * 配送方式
   */
  deliveryMethod?: DeliveryMethod;
  /**
   * 发货仓库id
   */
  warehouseId?: string;
  /**
   * 发货仓库名称
   */
  warehouseName?: string;
}

export interface DraftOrderItemList {
  /**
   * 品牌
   */
  brandName?: string;
  /**
   * 供应商编码
   */
  brandPartNo?: string;
  /**
   * 类目编码
   */
  categoryId?: string;
  /**
   * 类目
   */
  categoryName?: string;
  /**
   * 成本价,单位：分
   */
  costPrice?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * OE
   */
  oeNo?: string;
  /**
   * 原价,单位：分
   */
  originalPrice?: number;
  /**
   * 购买数量
   */
  saleNum?: number;
  /**
   * sku编码
   */
  skuId?: string;
  /**
   * 单位
   */
  unit?: string;
  /**
   * 售价,单位：分
   */
  unitPrice?: number;
}

/**
 * None
 */
export interface DraftOrderMain {
  /**
   * 客户id
   */
  cstId?: string;
  /**
   * 客户名称
   */
  cstName?: string;
  /**
   * 客户手机
   */
  cstPhone?: string;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 三方单号
   */
  thirdOrderNo?: string;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 汇率
   */
  exchangeRate?: number;
  /**
   * 关联单号
   */
  referenceNo?: string;
  /**
   * 订单渠道
   */
  channel?: OrderChannelCode;
  /**
   * 归属门店id
   */
  belongingStoreId?: string;
  /**
   * 归属门店名称
   */
  belongingStoreName?: string;
  /**
   * 客户不收税
   */
  gstExcluded?: number;
  /**
   * 制单人邮箱
   */
  creatorEmail?: string;
}

/**
 * None
 */
export interface DraftOrderPay {
  /**
   * 默认支付方式（0：额度账期、1：现金）com.ipms.sales.sale.api.enums.PayDetailChannelEnum
   */
  payChannel?: PayChannel;
  /**
   * 收款账户
   */
  payeeAcount?: string;
  /**
   * 一级支付方式
   */
  payKind?: PayKind;
}
