import Card from '@/components/Card';
import RadioButtonGroup from '@/components/RadioButtonGroup';
import { updateAdjustment } from '@/packages/sales/order/edit/services';
import {
  AdjustAmountType,
  adjustAmountOptions,
} from '@/packages/sales/order/edit/types/adjust.amount.type';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { Divider, Form, Input } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';

export interface DiscountProps {
  orderDetail?: OrderListItemEntity;
  onRefresh?: () => void;
}

const AdjustAmount = (props: DiscountProps) => {
  const [form] = Form.useForm();
  const { orderDetail, onRefresh } = props;
  const [localAdjustType, setLocalAdjustType] = useState<AdjustAmountType>(AdjustAmountType.None);

  /**
   * 订单值回显
   */
  useEffect(() => {
    if (orderDetail) {
      const { adjustmentType, adjustmentReason, adjustmentYuan } = orderDetail?.orderPrice ?? {};
      if (typeof adjustmentType !== 'undefined') {
        setLocalAdjustType(adjustmentType);
        form.setFieldsValue({
          adjustmentReason,
          adjustment: adjustmentYuan ? -adjustmentYuan : '',
        });
      }
    }
  }, [orderDetail]);

  if (!orderDetail) {
    return;
  }

  const onCustomChange = () => {
    const values = form.getFieldsValue(true);
    if (values.adjustment && values.adjustmentReason) {
      const price = convertStringToNumber({
        value: values.adjustment,
        min: 0.01,
        max: 999999999.99,
        decimal: 2,
      });
      Taro.showLoading();
      updateAdjustment({
        orderId: orderDetail?.orderId,
        adjustmentType: AdjustAmountType.Custom,
        adjustment: -price,
        adjustmentReason: values.adjustmentReason,
      })
        .then((result) => {
          if (result) {
            onRefresh?.();
          }
        })
        .finally(() => {
          Taro.hideLoading();
        });
    }
  };

  return (
    <Card title="调整金额">
      <Form form={form}>
        <RadioButtonGroup
          value={localAdjustType}
          options={adjustAmountOptions}
          onChange={(v: AdjustAmountType) => {
            setLocalAdjustType(v);
            if (v !== AdjustAmountType.Custom) {
              updateAdjustment({
                orderId: orderDetail?.orderId,
                adjustmentType: v,
              }).then((result) => {
                if (result) {
                  onRefresh?.();
                }
              });
            }
          }}
        />
        {localAdjustType === AdjustAmountType.Custom && (
          <>
            <Divider className="!my-1.5" />
            <Form.Item name="adjustment" label="调整金额">
              <Input
                type="digit"
                clearable={false}
                placeholder="请输入金额"
                onBlur={onCustomChange}
              />
            </Form.Item>
            <Form.Item name="adjustmentReason" label="调整原因">
              <Input clearable={false} placeholder="请输入调整原因" onBlur={onCustomChange} />
            </Form.Item>
          </>
        )}
      </Form>
    </Card>
  );
};

export default AdjustAmount;
