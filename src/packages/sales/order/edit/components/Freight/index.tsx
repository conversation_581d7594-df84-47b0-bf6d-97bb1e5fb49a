import Card from '@/components/Card';
import { updateExtendExpense } from '@/packages/sales/order/edit/services';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { Form, Input } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useEffect } from 'react';

export interface FreightProps {
  orderDetail?: OrderListItemEntity;
  onRefresh?: () => void;
}

export default function Freight(props: FreightProps) {
  const { orderDetail, onRefresh } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      deliveryAmount: orderDetail?.orderPrice?.deliveryAmountYuan?.toString(),
    });
  }, [orderDetail]);

  if (!orderDetail) {
    return;
  }

  return (
    <Card>
      <Form form={form}>
        <Form.Item name="deliveryAmount" label={<span className="text-[32px]">配送运费</span>}>
          <Input
            type="digit"
            clearable={false}
            placeholder="请输入"
            onBlur={(v) => {
              if (!v) {
                return;
              }
              const value = convertStringToNumber({
                value: v,
                min: 0.01,
                max: 999999999.99,
                decimal: 2,
              });
              Taro.showLoading();
              updateExtendExpense({
                orderId: orderDetail?.orderId,
                deliveryAmount: value,
              })
                .then((result) => {
                  if (result) {
                    onRefresh?.();
                  }
                })
                .finally(() => {
                  Taro.hideLoading();
                });
            }}
          />
        </Form.Item>
      </Form>
    </Card>
  );
}
