import {
  queryAdvanceAccount,
  queryMemberAccountPage,
} from '@/components/AccountSelectModal/services';
import { MemberAccountEntity } from '@/components/AccountSelectModal/types/MemberAccountEntity';
import Card from '@/components/Card';
import {
  AdvanceAmountList,
  CustomerEntity,
  ReceivableAmountList,
} from '@/components/ChooseCstModal/types/CustomerEntity';
import CustomPicker from '@/components/CustomPicker';
import RadioButtonGroup from '@/components/RadioButtonGroup';
import { PayChannel } from '@/packages/sales/order/edit/components/Settle/types/PayChannel';
import {
  PayKind,
  payKindOptions,
} from '@/packages/sales/order/edit/components/Settle/types/PayKind';
import { updateOrderPayKind } from '@/packages/sales/order/edit/services';
import { ConfirmPayRequest } from '@/packages/sales/order/edit/types/confirm.pay.request';
import { UpdateOrderPayKindRequest } from '@/packages/sales/order/edit/types/update.order.pay.kind.request';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { ArrowDown, IconFont, Trash } from '@nutui/icons-react-taro';
import { Divider, Input } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import _ from 'lodash';
import { useEffect, useRef, useState } from 'react';
import addIcon from './imgs/add.svg';

type AccountOption = { title: string; value: string; disabled?: boolean };

export interface CashListItem {
  payeeAcount: string;
  payAmount: string | undefined;
  key: string;
}

export interface SettleProps {
  orderDetail?: OrderListItemEntity;
  cstDetail?: CustomerEntity;
  onRefresh?: () => void;
  className?: string;
  onChange?: (data: UpdateOrderPayKindRequest) => void;
}

export const ADVANCE_ACCOUNT = 'ADVANCE_ACCOUNT';

export interface GetAccountListProps {
  list?: MemberAccountEntity[];
  cstDetail?: CustomerEntity;
  currency?: string;
  hasAdvance?: boolean;
}

export const getAccountList = (
  props: GetAccountListProps,
): { list: AccountOption[]; canUseAdvance: number } => {
  const { list = [], cstDetail, currency = 'AUD', hasAdvance = false } = props;

  if (!cstDetail) {
    return { list: [], canUseAdvance: 0 };
  }

  // 计算当前币种可用预收余额
  const advance = cstDetail?.settle?.advanceAmountList?.find(
    (item: AdvanceAmountList) => item.currency === currency,
  )?.amount;
  const receivable = cstDetail?.settle?.receivableAmountList?.find(
    (item: ReceivableAmountList) => item.currency === currency,
  )?.amount;
  const canUseAdvance = _.round(_.subtract(advance ?? 0, receivable ?? 0), 2);

  let result: AccountOption[] = [];
  if (hasAdvance) {
    result.push({
      title: '预收余额',
      value: ADVANCE_ACCOUNT,
    });
  }
  if (list?.length > 0) {
    result = result.concat(
      list.map((item) => ({
        title: item.memberAccountName,
        value: item.id,
      })),
    );
  }
  return { list: result, canUseAdvance };
};

const Settle = (props: SettleProps) => {
  const needUpdate = useRef(true);
  const lastSubmittedSigRef = useRef<string | null>(null);
  const { orderDetail, className, cstDetail, onRefresh, onChange } = props;
  const [accountList, setAccountList] = useState<AccountOption[]>([]);
  const [cashList, setCashList] = useState<CashListItem[]>([]);
  const [payKind, setPayKind] = useState<PayKind>();
  const [accountPickerProps, setAccountPickerProps] = useState<{
    visible: boolean;
    index?: number;
  }>({
    visible: false,
    index: undefined,
  });

  const storeId = orderDetail?.orders?.storeId;

  useEffect(() => {
    if (orderDetail) {
      setPayKind(orderDetail?.orderPayDetailList?.[0]?.payKind);
      if (orderDetail?.orderPayDetailList?.[0]?.payKind === PayKind.Cash) {
        const payDetailList: CashListItem[] = [];
        orderDetail?.orderPayDetailList?.forEach((item) => {
          if (item.payChannel === PayChannel.ADVANCE) {
            payDetailList.push({
              key: item.id!,
              payeeAcount: ADVANCE_ACCOUNT,
              payAmount: item.payAmountYuan?.toString(),
            });
          } else if (
            typeof item.payAmountYuan !== 'undefined' &&
            typeof item.payeeAccount !== 'undefined'
          ) {
            payDetailList.push({
              payeeAcount: item.payeeAccount,
              payAmount: item.payAmountYuan?.toString(),
              key: item.id!,
            });
          }
        });
        if (payDetailList.length) {
          setCashList(payDetailList);
        }
      }
    } else {
      setPayKind(cstDetail?.settle?.credit ? PayKind.Credit : PayKind.Cash);
    }
  }, [orderDetail]);

  useEffect(() => {
    if (storeId && cstDetail) {
      Promise.all([
        queryMemberAccountPage({
          belongToStore: [storeId],
        }),
        queryAdvanceAccount({ customerId: cstDetail?.base?.id, currency: 'AUD' }),
      ]).then((res) => {
        console.log(2222, res);
        const account = getAccountList({
          list: res?.[0]?.data,
          cstDetail,
          currency: 'AUD',
          hasAdvance: Boolean(res?.[1]?.id),
        });
        setAccountList(account.list);
      });
    }
  }, [storeId, cstDetail]);

  useEffect(() => {
    if (!orderDetail) {
      return;
    }
    const payKindParams = {
      orderId: orderDetail?.orderId,
      payKind: payKind,
      payDetailList: [],
    } as ConfirmPayRequest;
    switch (payKind) {
      case PayKind.Credit:
        payKindParams.payDetailList?.push({
          payChannel: PayChannel.PAYMENT_DAYS,
          payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
        });
        if (onChange) {
          onChange(payKindParams);
        } else {
          if (payKind !== orderDetail.orderPayDetailList?.[0].payKind) {
            updateOrderPayKind(payKindParams).then((result) => {
              if (result) {
                onRefresh?.();
              }
            });
          }
        }
        break;
      case PayKind.Cash:
        if (orderDetail.orderPayDetailList?.[0]?.payKind === PayKind.Credit) {
          setCashList([
            {
              payeeAcount: accountList?.[0]?.value,
              payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan?.toString(),
              key: _.uniqueId('cashList'),
            },
          ]);
        } else {
          payKindParams.payDetailList = orderDetail.orderPayDetailList?.map((item) => ({
            payeeAcount: item.payeeAccount,
            payAmount: item.payAmountYuan,
            payChannel: item.payKind,
          }));
          if (onChange) {
            onChange(payKindParams);
          }
        }
    }
  }, [payKind, orderDetail, accountList]);

  /**
   * 更新结算方式
   */
  useEffect(() => {
    if (!orderDetail) {
      return;
    }
    if (!needUpdate.current) {
      return;
    }
    if (payKind === PayKind.Credit) {
      return;
    }
    let payKindParams = {
      orderId: orderDetail?.orderId,
      payKind: payKind,
      payDetailList: [],
    } as ConfirmPayRequest;

    cashList?.forEach((item: any) => {
      payKindParams.payDetailList?.push({
        ...item,
        payChannel: PayChannel.CASH,
      });
    });

    if (onChange) {
      onChange(payKindParams);
    } else {
      // 校验空数据并在只有一个条目时自动补齐
      let hasEmpty = false;
      (payKindParams.payDetailList || []).forEach((item: any) => {
        if (!item?.payAmount || !item?.payeeAcount) {
          hasEmpty = true;
        }
      });

      if ((payKindParams?.payDetailList?.length || 0) === 1 && hasEmpty) {
        payKindParams = {
          ...payKindParams,
          payDetailList: [
            {
              payChannel: PayChannel.CASH,
              payeeAcount: accountList?.filter((item) => !item.disabled)?.[0]?.value,
              payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
            },
          ],
        } as ConfirmPayRequest;
        hasEmpty = false;
      }

      if (hasEmpty) return;

      // 生成提交参数（映射预收余额为 ADVANCE 渠道）
      const submitParams: ConfirmPayRequest = {
        ...payKindParams,
        payDetailList: (payKindParams.payDetailList || []).map((item: any) =>
          item.payeeAcount === ADVANCE_ACCOUNT
            ? ({ payChannel: PayChannel.ADVANCE, payAmount: item.payAmount } as any)
            : item,
        ),
      };

      // 基于稳定签名去重，避免循环调用
      const submitSig = JSON.stringify(
        (submitParams.payDetailList || [])
          .map((i: any) => ({
            payChannel: i.payChannel,
            payeeAcount: (i as any).payeeAcount ?? ADVANCE_ACCOUNT,
            payAmount: i.payAmount,
          }))
          .sort((a, b) =>
            `${a.payChannel}-${a.payeeAcount}`.localeCompare(`${b.payChannel}-${b.payeeAcount}`),
          ),
      );
      if (lastSubmittedSigRef.current === submitSig) return;
      lastSubmittedSigRef.current = submitSig;

      updateOrderPayKind(submitParams).then((result) => {
        if (result) onRefresh?.();
      });
    }
  }, [cashList]);

  /**
   * 选择付款账户
   * @param selectedItems
   */
  const handelSelectAccount = (selectedItems: string[]) => {
    if (selectedItems.length === 0) {
      return;
    }
    if (cashList.length === 2 && cashList?.[0].payeeAcount === selectedItems[0]) {
      Taro.showToast({ title: '请选择不同的账户', icon: 'none' });
      return;
    }
    setCashList(
      cashList.map((item, index) =>
        index === accountPickerProps.index ? { ...item, payeeAcount: selectedItems[0] } : item,
      ),
    );
  };

  if (!orderDetail) {
    return null;
  }

  return (
    <Card title="结算方式" className={className}>
      <RadioButtonGroup
        value={payKind}
        onChange={(v) => setPayKind(v as PayKind)}
        options={payKindOptions(cstDetail?.settle?.credit === true)}
      />
      {payKind === PayKind.Credit && (
        <div className="text-secondary text-[28px] mt-[24px]">
          可用
          {cstDetail?.settle?.availableAmount ?? '-'}
        </div>
      )}
      {payKind === PayKind.Cash && (
        <div className="text-[28px]">
          <div>
            {cashList?.map((item, index) => (
              <div className="flex items-center" key={item.key}>
                <div
                  className="flex self-center items-center"
                  onClick={() => setAccountPickerProps({ visible: true, index })}
                >
                  <div
                    className="overflow-ellipsis overflow-hidden whitespace-nowrap"
                    style={{ maxWidth: '300px' }}
                  >
                    {accountList?.find((m) => m.value === item?.payeeAcount)?.title ?? (
                      <div className="text-secondary">请选择</div>
                    )}
                  </div>
                  <ArrowDown size={20} className="ml-[10px]" color="#999999" />
                </div>
                <Input
                  className="flex-1 h-[108px] ml-[28px]"
                  placeholder="请输入金额"
                  value={item.payAmount}
                  onFocus={() => {
                    needUpdate.current = false;
                  }}
                  onChange={(v) => {
                    setCashList((prev) =>
                      prev.map((p, n) => (n === index ? { ...p, payAmount: v } : p)),
                    );
                  }}
                  onBlur={(v) => {
                    needUpdate.current = true;
                    if (!v) {
                      return;
                    }
                    const value = convertStringToNumber({
                      value: v,
                      min: 0,
                      max: 999999999.99,
                      decimal: 2,
                    });
                    setCashList((prev) =>
                      prev.map((p, n) => (n === index ? { ...p, payAmount: value.toString() } : p)),
                    );
                  }}
                />
                {cashList?.length === 2 && (
                  <Trash
                    size={20}
                    color="#777777"
                    onClick={() => {
                      setCashList((prev) => {
                        const next = _.cloneDeep(prev);
                        next.splice(index, 1);
                        return next;
                      });
                    }}
                  />
                )}
              </div>
            ))}
          </div>
          {cashList?.length === 1 && (
            <>
              <Divider />
              <div className="mt-[24px]">
                <div
                  className="flex text-[#F83431] justify-center items-center"
                  onClick={() => {
                    setCashList((prev) => [
                      ...prev,
                      { payeeAcount: '', payAmount: undefined, key: _.uniqueId('addAccount') },
                    ]);
                  }}
                >
                  <IconFont name={addIcon} style={{ width: '30px', marginRight: '10px' }} />
                  新增结算账户
                </div>
              </div>
            </>
          )}
        </div>
      )}
      <CustomPicker
        items={accountList}
        title="选择账户"
        visible={accountPickerProps.visible}
        selected={[cashList?.[accountPickerProps?.index!]?.payeeAcount]}
        onClose={() => setAccountPickerProps({ visible: false, index: undefined })}
        multiple={false}
        onConfirm={handelSelectAccount}
      />
    </Card>
  );
};

export default Settle;
