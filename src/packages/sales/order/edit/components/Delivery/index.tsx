import Card from '@/components/Card';
import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import RadioButtonGroup from '@/components/RadioButtonGroup';
import {
  updateOrderAddress,
  updateOrderDeliveryInfo,
  updateOrderTag,
} from '@/packages/sales/order/edit/services';
import {
  DeliveryMethod,
  deliveryMethodOptions,
} from '@/packages/sales/order/edit/types/DeliveryMethod';
import { UpdateOrderTagRequest } from '@/packages/sales/order/edit/types/update.order.tag.request';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { accountListQuerySimple } from '@/services/common';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Cell, DatePicker, Divider, Form, Input, Picker, Switch } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';

export interface DeliveryProps {
  orderDetail?: OrderListItemEntity;
  cstDetail?: CustomerEntity;
  onRefresh?: () => void;
}

const Delivery = (props: DeliveryProps) => {
  const [form] = Form.useForm();
  const { orderDetail, cstDetail, onRefresh } = props;
  const [estimatedDeliveryTime, setEstimatedDeliveryTime] = useState<string>();
  const [pickerVisible, setPickerVisible] = useState(false);
  const [postManList, setPostManList] = useState<any[]>([]);

  // 获取配送地址列表
  const addressList = useMemo(() => {
    const options: any = [];
    if (cstDetail?.addresses?.length) {
      cstDetail?.addresses.forEach((item) => {
        options.push({
          text: `${item.provinceName}${item.cityName}${item.prefectureName}${item.address}`,
          value: item.id,
        });
      });
    }
    return options;
  }, [cstDetail]);

  // 获取配送员列表
  useEffect(() => {
    accountListQuerySimple({}).then((res) => {
      setPostManList(res?.map((item) => ({ text: item.name, value: item.id })) ?? []);
    });
  }, []);

  const distributionMode = orderDetail?.orderFixedDistributionList?.[0]?.distributionMode;

  useEffect(() => {
    if (orderDetail) {
      const formData = {
        deliveryMethod: distributionMode,
        addressId: [orderDetail?.orderFixedAddressList?.[0].addressId],
        deliveryMan: [orderDetail?.orderFixedDistributionList?.[0]?.deliveryMan],
      };
      form.setFieldsValue(formData);
      const _estimatedDeliveryTime =
        orderDetail?.orderFixedDistributionList?.[0].estimatedDeliveryTime;
      setEstimatedDeliveryTime(
        _estimatedDeliveryTime ? dayjs(_estimatedDeliveryTime).format('YYYY-MM-DD') : '',
      );
    }
  }, [orderDetail]);

  if (!orderDetail) {
    return;
  }

  return (
    <Card title="配送信息">
      <Form form={form}>
        <Form.Item name="deliveryMethod">
          <RadioButtonGroup
            defaultValue={DeliveryMethod.MERCHANT_DELIVERY}
            options={deliveryMethodOptions}
            onChange={(value) => {
              updateOrderDeliveryInfo({
                orderId: orderDetail?.orderId,
                deliveryMethod: value as DeliveryMethod,
              }).then((result) => {
                if (result) {
                  onRefresh?.();
                }
              });
            }}
          />
        </Form.Item>
        {[DeliveryMethod.MERCHANT_DELIVERY, DeliveryMethod.EXPRESS_LOGISTICS].includes(
          distributionMode!,
        ) && (
          <Form.Item
            label="配送地址"
            name="addressId"
            trigger="onConfirm"
            getValueFromEvent={(...args) => args[1]}
            // @ts-ignore
            onClick={(event, ref: any) => {
              ref.open();
            }}
          >
            <Picker
              options={addressList}
              title="选择地址"
              onConfirm={(v) => {
                const address = cstDetail?.addresses?.find((item) => item.id === v?.[0].value);
                if (address) {
                  updateOrderAddress({ orderId: orderDetail?.orderId, ...address }).then(
                    (result) => {
                      if (result) {
                        onRefresh?.();
                      }
                    },
                  );
                }
              }}
            >
              {(value: any) => {
                return (
                  <Cell
                    title={
                      <div className="text-right">
                        {value.length ? (
                          addressList.filter((item) => item.value === value[0])[0]?.text
                        ) : (
                          <span className="text-thirdary">请选择</span>
                        )}
                      </div>
                    }
                    extra={<ArrowRight />}
                  />
                );
              }}
            </Picker>
          </Form.Item>
        )}
        {distributionMode === DeliveryMethod.MERCHANT_DELIVERY && (
          <Form.Item
            label="配送员"
            name="deliveryMan"
            trigger="onConfirm"
            getValueFromEvent={(...args) => args[1]}
            // @ts-ignore
            onClick={(event, ref: any) => {
              ref.open();
            }}
          >
            <Picker
              options={postManList}
              title="选择配送员"
              onConfirm={(v) => {
                updateOrderDeliveryInfo({
                  orderId: orderDetail?.orderId,
                  deliveryMan: v?.[0]?.value as string,
                }).then((result) => {
                  if (result) {
                    onRefresh?.();
                  }
                });
              }}
            >
              {(value: any) => {
                return (
                  <Cell
                    title={
                      <div className="text-right">
                        {value.length ? (
                          postManList.filter((item) => item.value === value[0])[0]?.text
                        ) : (
                          <span className="text-thirdary">请选择</span>
                        )}
                      </div>
                    }
                    extra={<ArrowRight />}
                  />
                );
              }}
            </Picker>
          </Form.Item>
        )}
        {distributionMode === DeliveryMethod.EXPRESS_LOGISTICS && (
          <>
            <Form.Item label="物流公司" name="logisticsCompanyName">
              <Input
                onBlur={(v) => {
                  updateOrderDeliveryInfo({
                    orderId: orderDetail?.orderId,
                    logisticsCompanyName: v,
                  }).then((result) => {
                    if (result) {
                      onRefresh?.();
                    }
                  });
                }}
              />
            </Form.Item>
            <Form.Item label="物流单号" name="logisticsNo">
              <Input
                onBlur={(v) => {
                  updateOrderDeliveryInfo({
                    orderId: orderDetail?.orderId,
                    logisticsNo: v,
                  }).then((result) => {
                    if (result) {
                      onRefresh?.();
                    }
                  });
                }}
              />
            </Form.Item>
          </>
        )}
        <Divider />
        <Cell
          title="期望送达时间"
          extra={
            <span className="flex just-center">
              {estimatedDeliveryTime}
              <ArrowRight />
            </span>
          }
          style={{ padding: '14px 0' }}
          onClick={() => setPickerVisible(true)}
        />
        <Divider />
        <Cell
          title="紧急订单"
          extra={
            <Switch
              checked={orderDetail?.orders?.orderTagList?.includes(2)}
              onChange={(v) => {
                const updateOrderTagParams: UpdateOrderTagRequest = {
                  orderId: orderDetail?.orderId,
                  operatorType: v ? 1 : 2,
                  tagId: 2,
                };
                Taro.showLoading();
                updateOrderTag(updateOrderTagParams).finally(() => {
                  onRefresh?.();
                  Taro.hideLoading();
                });
              }}
            />
          }
          style={{ padding: '14px 0' }}
        />
      </Form>
      <DatePicker
        title="期望送达时间"
        startDate={dayjs().toDate()}
        defaultValue={dayjs().toDate()}
        visible={pickerVisible}
        onConfirm={(_options, values) => {
          const data = values.join('-');
          setEstimatedDeliveryTime(data);
          updateOrderDeliveryInfo({
            orderId: orderDetail?.orderId,
            estimatedDeliveryTime: data,
          }).then((result) => {
            if (result) {
              onRefresh?.();
            }
          });
        }}
        onClose={() => setPickerVisible(false)}
      />
    </Card>
  );
};

export default Delivery;
