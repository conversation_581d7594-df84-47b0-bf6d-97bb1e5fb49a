import Card from '@/components/Card';
import { updateOrderRemark } from '@/packages/sales/order/edit/services';
import {
  RemarkType,
  UpdateOrderRemarkRequest,
} from '@/packages/sales/order/edit/types/update.order.remark.request';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { ArrowDown, ArrowUp } from '@nutui/icons-react-taro';
import { ConfigProvider, Divider, TextArea } from '@nutui/nutui-react-taro';
import { useEffect, useState } from 'react';

export interface RemarkProps {
  orderDetail?: OrderListItemEntity;
  onRefresh?: () => void;
}

const Remark = (props: RemarkProps) => {
  const { orderDetail } = props;
  const [saleRemark, setSaleRemark] = useState<string>();
  const [innerRemark, setInnerRemark] = useState<string>();
  const [unfold, setUnfold] = useState(false);

  useEffect(() => {
    setSaleRemark(
      orderDetail?.orderNoteList?.find((item) => item.noteType === RemarkType.StoreToCustomer)
        ?.noteDetail,
    );
    setInnerRemark(
      orderDetail?.orderNoteList?.find((item) => item.noteType === RemarkType.StoreToInner)
        ?.noteDetail,
    );
  }, [orderDetail]);

  if (!orderDetail) {
    return;
  }

  const handleChange = (remarkType: RemarkType) => {
    const data: UpdateOrderRemarkRequest = { remarkType, orderId: orderDetail?.orderId };
    switch (remarkType) {
      case RemarkType.StoreToCustomer:
        data.remark = saleRemark?.trim();
        break;
      case RemarkType.StoreToInner:
        data.remark = innerRemark?.trim();
        break;
    }
    if (!data.remark) {
      return;
    }
    updateOrderRemark(data);
  };

  return (
    <Card
      className="pb-0.5"
      title={
        <div
          className="flex justify-between items-center"
          onClick={() => {
            setUnfold(!unfold);
          }}
        >
          <span>备注</span>
          <span>{unfold ? <ArrowUp /> : <ArrowDown />}</span>
        </div>
      }
    >
      <ConfigProvider theme={{ nutuiTextareaPadding: '0' }}>
        {unfold && (
          <>
            <div className="text-[32px] mb-2">销售备注</div>
            <TextArea
              style={{ height: '100px' }}
              value={saleRemark}
              name="remark"
              maxLength={100}
              showCount={true}
              onChange={setSaleRemark}
              onBlur={() => handleChange(RemarkType.StoreToCustomer)}
              placeholder="请输入备注信息"
            />
            <Divider />
            <div className="text-[32px] mt-3 mb-2">内部备注</div>
            <TextArea
              style={{ height: '100px' }}
              value={innerRemark}
              name="remark"
              maxLength={100}
              showCount={true}
              onChange={setInnerRemark}
              onBlur={() => handleChange(RemarkType.StoreToInner)}
              placeholder="请输入备注信息"
            />
          </>
        )}
      </ConfigProvider>
    </Card>
  );
};

export default Remark;
