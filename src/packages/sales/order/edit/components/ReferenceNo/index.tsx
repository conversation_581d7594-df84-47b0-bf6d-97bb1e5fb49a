import Card from '@/components/Card';
import { updateOrderMain } from '@/packages/sales/order/edit/services';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { Form, Input } from '@nutui/nutui-react-taro';
import { useEffect } from 'react';

export interface ReferenceNoProps {
  orderDetail?: OrderListItemEntity;
  onRefresh?: () => void;
}

export default function ReferenceNo(props: ReferenceNoProps) {
  const { orderDetail } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      referenceNo: orderDetail?.orders?.referenceNo,
    });
  }, [orderDetail]);

  if (!orderDetail) {
    return;
  }

  return (
    <Card>
      <Form form={form}>
        <Form.Item name="referenceNo" label={<span className="text-[32px]">Reference No</span>}>
          <Input
            clearable={false}
            onBlur={(v) => {
              updateOrderMain({
                orderId: orderDetail?.orders?.orderId,
                referenceNo: v,
              });
            }}
          />
        </Form.Item>
      </Form>
    </Card>
  );
}
