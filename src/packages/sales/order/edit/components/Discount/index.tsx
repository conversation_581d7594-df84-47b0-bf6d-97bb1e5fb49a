import Card from '@/components/Card';
import RadioButtonGroup from '@/components/RadioButtonGroup';
import { updateOrderDiscount } from '@/packages/sales/order/edit/services';
import { DiscountType, discountTypeOptions } from '@/packages/sales/order/edit/types/DiscountType';
import { CouponEntity } from '@/packages/sales/order/edit/types/coupon.entity';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Cell, Form, Input, Picker } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';

export interface DiscountProps {
  orderDetail?: OrderListItemEntity;
  couponList?: CouponEntity[];
  onRefresh?: () => void;
}

const Discount = (props: DiscountProps) => {
  const [form] = Form.useForm();
  const { orderDetail, onRefresh, couponList = [] } = props;
  const [localDiscountType, setLocalDiscountType] = useState<DiscountType>(DiscountType.NONE);

  /**
   * 订单值回显
   */
  useEffect(() => {
    if (orderDetail) {
      const defaultData: any = {};
      const defaultDiscountType =
        orderDetail?.orderFixedDiscountList?.[0]?.discountType ?? DiscountType.NONE;
      defaultData.discountType = defaultDiscountType;
      form.setFieldsValue(defaultData);
      setLocalDiscountType(defaultDiscountType);

      resetRateAndDiscount();
    }
  }, [orderDetail]);

  /**
   * 回显折扣和金额
   */
  const resetRateAndDiscount = () => {
    if (orderDetail) {
      const defaultData: any = {};

      const defaultDiscountMoney = orderDetail?.orderFixedDiscountList?.[0]?.discountMoneyYuan;
      if (defaultDiscountMoney) {
        defaultData.discountMoney = defaultDiscountMoney;
      }

      if (orderDetail.orderFixedDiscountList?.[0]?.discountDesc !== 'null') {
        const defaultDiscountRate = orderDetail?.orderFixedDiscountList?.[0]?.discountDesc;
        if (defaultDiscountRate) {
          defaultData.discountRate = defaultDiscountRate;
        }
      }
      form.setFieldsValue(defaultData);
    }
  };

  if (!orderDetail) {
    return;
  }

  return (
    <Card title="优惠类型">
      {couponList.length > 0 && <div className="text-red-600 text-[24px]">存在可用优惠券</div>}
      <Form form={form}>
        <Form.Item name="discountType">
          <RadioButtonGroup
            value={localDiscountType}
            options={discountTypeOptions}
            onChange={(v) => {
              setLocalDiscountType(v as DiscountType);
              if (v === DiscountType.NONE) {
                updateOrderDiscount({
                  discountType: v,
                  orderId: orderDetail?.orderId,
                }).then((result) => {
                  if (result) {
                    onRefresh?.();
                  }
                });
              } else {
                resetRateAndDiscount();
              }
            }}
          />
        </Form.Item>
        {localDiscountType === DiscountType.COUPON && (
          <Form.Item
            label="优惠券"
            name="couponId"
            trigger="onConfirm"
            getValueFromEvent={(...args) => args[1]}
            // @ts-ignore
            onClick={(event, ref: any) => {
              if (couponList.length > 0) {
                ref.open();
              }
            }}
          >
            <Picker
              options={couponList.map((item) => ({
                text: item.couponName!,
                value: item.userCouponId!.toString(),
              }))}
              title="选择优惠券"
              onConfirm={(v) => {
                const couponId = v[0].value as number;
                Taro.showLoading();
                updateOrderDiscount({
                  discountType: localDiscountType,
                  orderId: orderDetail?.orderId,
                  couponId: couponId?.toString(),
                  discountMoney: couponList.find((item) => item.userCouponId == couponId)
                    ?.couponAmount,
                })
                  .then((result) => {
                    if (result) {
                      onRefresh?.();
                    }
                  })
                  .finally(() => {
                    Taro.hideLoading();
                  });
              }}
            >
              {() => {
                return (
                  <Cell
                    title={
                      <div className="text-right">
                        {couponList.filter(
                          (item) =>
                            item.userCouponId ==
                            orderDetail?.orderFixedDiscountList?.[0]?.discountItemId,
                        )[0]?.couponName ??
                          (couponList.length === 0 ? (
                            <span className="text-thirdary">无可用优惠券</span>
                          ) : (
                            <span className="text-thirdary">请选择</span>
                          ))}
                      </div>
                    }
                    extra={<ArrowRight />}
                  />
                );
              }}
            </Picker>
          </Form.Item>
        )}
        {localDiscountType === DiscountType.DISCOUNT_ON_ORDER && (
          <Form.Item name="discountRate" label="打折">
            <Input
              type="digit"
              placeholder="请输入折扣"
              clearable={false}
              onBlur={(v) => {
                if (!v) {
                  return;
                }
                if (v === orderDetail?.orderFixedDiscountList?.[0]?.discountDesc) {
                  return;
                }
                const value = convertStringToNumber({
                  value: v,
                  min: 0.1,
                  max: 9.9,
                  decimal: 1,
                });
                if (value.toString() !== v) {
                  form.setFieldsValue({ discountRate: value });
                }
                updateOrderDiscount({
                  discountType: localDiscountType,
                  orderId: orderDetail?.orderId,
                  discountRate: value,
                }).then((result) => {
                  if (result) {
                    onRefresh?.();
                  }
                });
              }}
            />
          </Form.Item>
        )}
        {localDiscountType === DiscountType.DEDUCTION_ON_ORDER && (
          <Form.Item name="discountMoney" label="减去">
            <Input
              type="digit"
              clearable={false}
              placeholder="请输入金额"
              onBlur={(v) => {
                if (!v) {
                  return;
                }
                if (Number(v) === orderDetail?.orderFixedDiscountList?.[0]?.discountMoneyYuan) {
                  return;
                }
                const value = convertStringToNumber({
                  value: v,
                  min: 0.01,
                  max: 999999999.99,
                  decimal: 2,
                });
                if (value.toString() !== v) {
                  form.setFieldsValue({ discountMoney: value });
                }
                updateOrderDiscount({
                  discountType: localDiscountType,
                  orderId: orderDetail?.orderId,
                  discountMoney: value,
                }).then((result) => {
                  if (result) {
                    onRefresh?.();
                  }
                });
              }}
            />
          </Form.Item>
        )}
      </Form>
    </Card>
  );
};

export default Discount;
