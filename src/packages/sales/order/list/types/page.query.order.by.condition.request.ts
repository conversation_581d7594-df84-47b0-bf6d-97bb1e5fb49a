export interface PageQueryOrderByConditionRequest {
  /**
   * 查询条件-创建时间开始
   */
  beginOrderTime?: string;
  /**
   * 客户id
   */
  cstId?: number;
  /**
   * 发货仓
   */
  deliWarehouseIdList?: string[];
  /**
   * 查询条件-创建时间结束
   */
  endOrderTime?: string;
  /**
   * 商品名称（模糊）商品编码（精确）OE（模糊）供应商编码（模糊）查询
   */
  itemInfo?: string;
  /**
   * 零售商id
   */
  memberId?: string;
  /**
   * 单个销售单号，精确匹配
   */
  orderNo?: string;
  /**
   * 多个销售单状态，精确匹配
   */
  orderStatusList?: number[];
  /**
   * 当前页码
   */
  pageNum?: number;
  /**
   * 每页数量
   */
  pageSize?: number;
  /**
   * 收款状态
   */
  paymentStatusList?: number[];
  /**
   * 支付状态
   */
  payStatusList?: number[];
  /**
   * 销售单备注，模糊匹配
   */
  remark?: string;
  /**
   * 员工查询
   */
  salesmanIdList?: string[];
  /**
   * 门店查询
   */
  storeIdList?: string[];
}
