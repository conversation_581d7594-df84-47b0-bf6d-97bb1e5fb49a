import CodeWithDivider from '@/components/CodeWithDivider';
import { StoreGoodsEntity } from '@/packages/sales/order/chooseGoodsPage/types/store.goods.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import {
  Button,
  ConfigProvider,
  Divider,
  Input,
  InputNumber,
  Popup,
  Price,
  SafeArea,
  Space,
  Tag,
} from '@nutui/nutui-react-taro';
import { ScrollView } from '@tarojs/components';
import Taro from '@tarojs/taro';
import _ from 'lodash';
import { useEffect, useState } from 'react';

export interface ItemData {
  price: number;
  number: number;
}

export interface GoodDetailPopupProps {
  visible: boolean;
  onClose: () => void;
  record?: StoreGoodsEntity;
  cstId?: string;
  warehouseId?: string;
  storeId?: string;
  localPrice?: number;
  localNumber?: number;
  onConfirm?: (data: ItemData) => void;
}

const GoodDetailPopup = (props: GoodDetailPopupProps) => {
  const {
    visible,
    cstId,
    warehouseId,
    onConfirm,
    storeId,
    onClose,
    record = {} as StoreGoodsEntity,
  } = props;
  const [localPrice, setLocalPrice] = useState<number>();
  const [localNumber, setLocalNumber] = useState<number>();

  useEffect(() => {
    setLocalPrice(props.localPrice ?? record?.suggestPrice);
  }, [props.localPrice, record]);

  useEffect(() => {
    setLocalNumber(props.localNumber);
  }, [props.localNumber]);

  const goodsInfo = [
    {
      label: '商品编码',
      value: record.itemSn,
    },
    {
      label: '供应商编码',
      value: (
        <CodeWithDivider
          key={'brandPartNos'}
          title="供应商编码"
          items={record.brandPartNos ?? []}
        />
      ),
    },
    {
      label: '品牌',
      value: record.brandName,
    },
    {
      label: '分类',
      value: record.categoryName,
    },
    {
      label: '商品标签',
      value: record.itemTagList?.map((item) => item.tagName),
    },
    {
      label: '单位',
      value: record.unitName,
    },
    {
      label: '商品备注',
      value: record.remark,
      width: 'w-full',
    },
  ];

  const handleConfirm = () => {
    if (!localPrice) {
      Taro.showToast({ icon: 'none', title: '请输入价格' });
      return;
    }
    if (!localNumber) {
      Taro.showToast({ icon: 'none', title: '请输入数量' });
      return;
    }
    onConfirm?.({ price: localPrice, number: localNumber });
    onClose?.();
  };

  return (
    <Popup
      visible={visible}
      title={record.itemName}
      position="bottom"
      closeable={true}
      onClose={onClose}
      destroyOnClose={true}
    >
      <ScrollView className="max-h-[25vh]" scrollY={true}>
        <div
          className="mx-[28px] p-[28px] rounded-[8px] flex flex-wrap gap-y-1"
          style={{ background: 'rgba(0, 0, 0, 0.03)' }}
        >
          {goodsInfo.map((item) => (
            <div
              className={`text-[28px] flex leading-[1.6] flex-shrink-0 ${item.width ?? 'w-[50%]'}`}
            >
              <span className="text-secondary whitespace-nowrap mr-1">{item.label}:</span>
              <span>{item.value}</span>
            </div>
          ))}
        </div>
      </ScrollView>
      <div className="flex items-center gap-1 mx-[28px] mt-[16px] -mb-[28px] p-[16px] bg-[#F6D7D5] text-[24px] rounded-[8px]">
        <Tag type="primary">活动</Tag>当前商品参与商城活动，可在门店工作台查看及下单
      </div>
      <div className="p-[28px]">
        <div className="flex items-center my-[20px]">
          <div>销售价格</div>
          <div className="flex flex-1 justify-between items-center">
            <Input
              placeholder="请输入价格"
              value={localPrice?.toString()}
              align={'center'}
              type="digit"
              maxLength={12}
              // @ts-ignore
              onChange={(v) => setLocalPrice(v)}
              onBlur={(v) => {
                const value = convertStringToNumber({
                  value: v,
                  min: 0,
                  max: 999999999.99,
                  decimal: 2,
                });
                setLocalPrice(value);
              }}
            />
            <span className="text-thirdary text-[28px]">
              上次售价：{record.lastSalePrice ?? '-'}
            </span>
          </div>
        </div>
        <Divider />
        <div className="flex items-center my-[20px] justify-between">
          <div className="mr-[50px]">销售数量</div>
          <InputNumber
            max={999999}
            min={0}
            value={localNumber}
            onChange={(v) => {
              setLocalNumber(Number(v) > 999999 ? 999999 : Number(v));
            }}
          />
          <div className="flex flex-col items-end gap-1">
            <div className="text-thirdary text-[28px]">本地库存：{record.avaNum}</div>
            <div className="text-thirdary text-[28px]">总库存：{record.inventoryNum}</div>
          </div>
        </div>
        <Divider />
        <ConfigProvider
          theme={{
            nutuiSpaceGap: '40px',
          }}
        >
          <Space className="text-[28px] my-[20px]">
            <a
              onClick={() =>
                Taro.navigateTo({
                  url: `/packages/sales/order/pricePage/index?itemId=${record.itemId}&cstId=${cstId}&storeId=${storeId}&lowPrice=${record.lowPrice}&costPrice=${record.costPrice}&suggestPrice=${record.suggestPrice}`,
                })
              }
            >
              价格参考
            </a>
            <a
              onClick={() =>
                Taro.navigateTo({
                  url: `/packages/sales/order/purchaseHistoryPage/index?itemId=${record.itemId}`,
                })
              }
            >
              采购历史
            </a>
            <a
              onClick={() =>
                Taro.navigateTo({
                  url: `/packages/sales/order/stocksPage/index?itemId=${record.itemId}&warehouseId=${warehouseId}`,
                })
              }
            >
              库存分布
            </a>
          </Space>
        </ConfigProvider>
      </div>
      <Divider />
      <div className="p-[28px]">
        <div className="flex justify-between">
          <Price price={_.round(_.multiply(localPrice ?? 0, localNumber ?? 0), 2)} />
          <Button
            type={'primary'}
            size={'large'}
            onClick={handleConfirm}
            style={{ width: '160px' }}
          >
            确定
          </Button>
        </div>
        <SafeArea position={'bottom'} />
      </div>
    </Popup>
  );
};

export default GoodDetailPopup;
