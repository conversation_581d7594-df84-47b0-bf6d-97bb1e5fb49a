import CustTag from '@/components/CustTag';
import Descriptions, { Column, ValueType } from '@/components/Descriptions';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import {
  ConfigProvider,
  Divider,
  Input,
  InputNumber,
  Popup,
  Price,
  SafeArea,
  TextArea,
} from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { defaultTo, isEmpty, multiply, size } from 'lodash';
import { useShallow } from 'zustand/react/shallow';
import { addItem, createOrder, modifyItem } from '../../../operation/services';
import useSalesReturnsStore from '../../../operation/store';
import returnsItemSelectStore from '../../store';
import { ReturnsTypeEnum } from '../../types/ReturnsTypeEnum';
export interface ReturnsItemModalProps {
  returnsType: string;
}
export default (props: ReturnsItemModalProps) => {
  const { returnsType = ReturnsTypeEnum.GOODS } = props;
  const columns: Column[] = [
    {
      title: '商品编码',
      key: 'itemSn',
    },
    {
      title: 'OE',
      key: 'oeNos',
      valueType: ValueType.ARRAY,
    },
    {
      title: '供应商编码',
      key: 'brandPartNos',
      valueType: ValueType.ARRAY,
    },
    {
      title: '商品品牌',
      key: 'brandName',
    },
    {
      title: '商品分类',
      key: 'categoryName',
    },
    {
      title: '单位',
      key: 'unitName',
    },
  ];
  const SalesReturnsStore = useSalesReturnsStore(
    useShallow((store) => ({
      refundType: store.refundType,
      orderId: store.orderId,
      orderNo: store.orderNo,
      cstId: store.cstId,
      storeId: store.storeId,
      backWarehouseId: store.backWarehouseId,
      setState: store.setState,
    })),
  );
  const { visible, setVisible, currentItem, setCurrentItem, reloadChooseItems } =
    returnsItemSelectStore(
      useShallow((store) => ({
        visible: store.visible,
        setVisible: store.setVisible,
        currentItem: store.currentItem,
        setCurrentItem: store.setCurrentItem,
        reloadChooseItems: store.reloadChooseItems,
      })),
    );

  // 不用展示ETC号
  // if (returnsType === ReturnsTypeEnum.ORDER) {
  //   columns.push({
  //     title: 'ETC号',
  //     key: 'etcNo',
  //   });
  // }
  // 校验金额
  const checkAmountAndCount = (currentItem) => {
    // 退款数量
    let refundNum = currentItem?.refundNum ?? 0;
    // 退款金额
    let refundAmount = currentItem?.unitAmount ?? 0;
    if (refundAmount < 0) {
      Taro.showToast({ icon: 'none', title: '请输入退货金额！' });
      return false;
    }
    if (refundNum < 1) {
      Taro.showToast({ icon: 'none', title: '请输入退货数量！' });
      return false;
    }
    return true;
  };
  const onCancel = () => {
    setVisible(false);
    setCurrentItem({});
  };
  // 确认提交
  const handleConfirm = async () => {
    if (!checkAmountAndCount(currentItem)) return;
    // 修改退货记录
    if (SalesReturnsStore.orderId) {
      let result: boolean = false;
      if (currentItem?.id) {
        result = await modifyItem({
          id: currentItem.id,
          orderId: SalesReturnsStore.orderId,
          orderNo: SalesReturnsStore.orderNo,
          refundNum: currentItem?.refundNum,
          unitAmount: currentItem?.unitAmount,
          cause: currentItem?.cause,
        });
      } else {
        result = await addItem({
          orderId: SalesReturnsStore.orderId,
          orderNo: SalesReturnsStore.orderNo,
          items: [
            {
              itemId: currentItem.itemId,
              refundNum: currentItem.refundNum,
              unitAmount: currentItem.unitAmount,
              costAmount: currentItem?.costAmount,
              saleNum: currentItem?.saleNum,
              orgOrderNo: currentItem?.orgOrderNo,
              payKind: currentItem?.payKind,
            },
          ],
        });
      }
      if (result) {
        reloadChooseItems({
          orderId: SalesReturnsStore.orderId,
          orderNo: SalesReturnsStore.orderNo,
        });
      }
    } else {
      // 新增草稿单
      const item = {
        itemId: currentItem?.itemId,
        refundNum: currentItem?.refundNum,
        unitAmount: currentItem?.unitAmount,
        costAmount: currentItem?.costAmount,
        saleNum: currentItem?.saleNum,
        orgOrderNo: currentItem?.orgOrderNo,
        cause: currentItem?.cause,
        payKind: currentItem?.payKind,
      };
      // 创建草稿单
      const result = await createOrder({
        main: {
          cstId: SalesReturnsStore.cstId,
          storeId: SalesReturnsStore.storeId,
          backWarehouseId: SalesReturnsStore.backWarehouseId,
        },
        refund: {
          refundType: SalesReturnsStore.refundType,
        },
        items: [item],
      });
      if (!isEmpty(result)) {
        SalesReturnsStore.setState('orderId', result.orderId);
        SalesReturnsStore.setState('orderNo', result.orderNo);
        reloadChooseItems(result);
      }
    }
    onCancel();
  };

  return (
    <Popup
      visible={visible}
      title={
        <div className="flex justify-start items-center gap-[10px]">
          <span className="text-main text-[32px]">{currentItem?.itemName}</span>
          {ReturnsTypeEnum.ORDER === returnsType && <CustTag text={currentItem?.payKindName} />}
        </div>
      }
      destroyOnClose={true}
      position="bottom"
      closeable={true}
      onClose={onCancel}
    >
      {returnsType == ReturnsTypeEnum.ORDER && (
        <div className="flex justify-between items-center pb-[24px] px-[28px]">
          <span className="text-main  text-[28px]">{currentItem?.orgOrderNo}</span>
          <span className="text-thirdary text-[24px]">{currentItem?.orderFinishTime}</span>
        </div>
      )}
      <div className="px-[28px]">
        <Descriptions columns={columns} record={currentItem}></Descriptions>
      </div>
      <div className="p-[28px]">
        <div className="flex justify-between items-center my-[20px]">
          <div className="text-main text-[32px]">退款金额</div>
          <Input
            type="digit"
            align="center"
            placeholder="请输入退款金额"
            value={`${currentItem?.unitAmount}`}
            maxLength={12}
            onChange={(value: string) => {
              setCurrentItem({ ...currentItem, unitAmount: value });
            }}
            // onBlur={(v) => {
            //   const value = convertStringToNumber({
            //     value: v,
            //     min: 0,
            //     max: 999999999.99,
            //     decimal: 2,
            //   });
            //   setCurrentItem({ ...currentItem, unitAmount: `${value}` });
            // }}
          />
          <span>
            {ReturnsTypeEnum.ORDER === returnsType && (
              <span className="text-thirdary text-[28px]">
                实付单价：{currentItem?.originUnitAmount}
              </span>
            )}
          </span>
        </div>
        <Divider />
        <div className="flex justify-between items-center my-[20px]">
          <div className="text-main text-[32px]">退货数量</div>
          <InputNumber
            max={999999}
            min={0}
            value={currentItem?.refundNum}
            onChange={(value: number) => {
              setCurrentItem({ ...currentItem, refundNum: value <= 999999 ? value : 999999 });
            }}
            onBlur={(e: any) => {
              const value = convertStringToNumber({
                value: e?.detail?.value ?? '1',
                min: 0,
                max: 999999,
                decimal: 0,
              });
              setCurrentItem({ ...currentItem, refundNum: value });
            }}
          />
          <span>
            {ReturnsTypeEnum.ORDER === returnsType && (
              <span className="text-thirdary text-[28px]">
                可退数量：{currentItem?.refundableNum}
              </span>
            )}
          </span>
        </div>
        <Divider />
        <div className="flex justify-between my-[24px]">
          <span className="text-main text-[32px]">退货原因</span>
          <span className="text-thirdary">{size(defaultTo(currentItem.cause, ''))}/100</span>
        </div>
        <ConfigProvider
          theme={{
            nutuiTextareaPadding: '0px 0px',
          }}
        >
          <TextArea
            cursorSpacing={32}
            autoSize
            maxLength={100}
            placeholder="请输入备注信息"
            onChange={(cause: string) => {
              setCurrentItem({ ...currentItem, cause: cause });
            }}
            value={defaultTo(currentItem?.cause, '')}
          />
        </ConfigProvider>
      </div>
      <Divider />
      <div className="p-[28px]">
        <div className="flex justify-between items-center relative z-50">
          <Price
            price={multiply(Number(currentItem?.unitAmount ?? '0'), currentItem?.refundNum ?? 1)}
          />

          <View
            className="px-[88px] py-[14px] text-white bg-primary rounded-[8px] text-[32px] flex justify-center items-center"
            onTap={(e) => {
              e.stopPropagation;
              handleConfirm();
            }}
          >
            确定
          </View>
        </div>
        <SafeArea position="bottom" />
      </div>
    </Popup>
  );
};
