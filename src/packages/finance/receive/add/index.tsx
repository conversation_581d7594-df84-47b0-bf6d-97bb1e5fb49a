import '@/assets/lightPage.scss';
import AccountSelectModal from '@/components/AccountSelectModal';
import Card from '@/components/Card';
import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import CstCard from '@/components/CstCard';
import { FinReceivableEntity } from '@/packages/finance/receive/list/types/FinReceivableEntity.entity';
import { queryReceivableList, receivedConfirmation } from '@/packages/finance/receive/services';
import { checkAmount, showToast } from '@/utils/financeUtils';
import { ArrowDown, IconFont } from '@nutui/icons-react-taro';
import { Button, Divider, Input, Price, Radio, Space, TextArea } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useAsyncEffect } from 'ahooks';
import classNames from 'classnames';
import _ from 'lodash';
import { useState } from 'react';
import CustomNavBar from '../../../../components/CustomNavBar';
import { AdjustType } from '../list/types/ReceivedEntityEnum';
import infoIcon from './images/info.svg';
import './index.scss';

export default function Index() {
  const [chooseCst, setChooseCst] = useState<CustomerEntity>();
  const [isAdding, setIsAdding] = useState<boolean>(false);
  const [visibleAccount, setVisibleAccount] = useState<boolean>(false);
  const [accountId, setAccountId] = useState<string>(null);
  const [accountName, setAccountName] = useState<string>(null);
  const [adjustType, setAdjustType] = useState<AdjustType>(AdjustType.NONE);
  const [receivableList, setReceivableList] = useState<FinReceivableEntity[]>([]);
  const [remark, setRemark] = useState('');
  const [totalReceivableAmountYuan, setTotalReceivableAmountYuan] = useState<string>('');
  const [currTotalReceivableAmountYuan, setCurrTotalReceivableAmountYuan] = useState<string>('');

  useAsyncEffect(async () => {
    await chooseCstHandle();
  }, [chooseCst]);

  const chooseCstHandle = async () => {
    if (!chooseCst) {
      setChooseCst(null);
      return;
    }
    setReceivableList([]);
    setRemark('');
    setTotalReceivableAmountYuan('');
    setCurrTotalReceivableAmountYuan('');

    const data = await queryReceivableList({ buyerId: chooseCst.base?.id, receivableFlag: 1 });
    setReceivableList(data ?? []);
  };

  const handleUpdate = (payableId, inputValue) => {
    const updatedReceivableList = receivableList.map((item) =>
      item.id === payableId ? { ...item, currReceivedAmount: inputValue } : item,
    );

    let totalReceivableAmount = 0;
    updatedReceivableList.forEach((item) => {
      if (item.id == payableId) {
        item.currReceivedAmount = inputValue;
      }
      if (item.currReceivedAmount) {
        totalReceivableAmount = _.round(
          _.add(Number(item.currReceivedAmount), totalReceivableAmount),
          2,
        );
      }
    });
    setReceivableList(updatedReceivableList);
    setCurrTotalReceivableAmountYuan(totalReceivableAmount.toString());
  };

  const autoAssignOrders = () => {
    let inTotalReceivableAmountYuan = Number(totalReceivableAmountYuan);
    const oldTotalReceivableAmountYuan = Number(totalReceivableAmountYuan);
    if (!inTotalReceivableAmountYuan) {
      showToast('收款金额不能为空！');
      return;
    }
    if (!chooseCst) {
      showToast('请先选择供应商！');
      return;
    }
    if (inTotalReceivableAmountYuan <= 0) {
      showToast('收款金额小于等于0，请手动分配核销金额！');
      return;
    }
    const newReceivableList = receivableList.map((item) => {
      if (inTotalReceivableAmountYuan == 0) {
        item.currReceivedAmount = undefined;
        return item;
      }
      if (inTotalReceivableAmountYuan >= item.remainReceivableAmountYuan) {
        item.currReceivedAmount = item.remainReceivableAmountYuan?.toString();
        inTotalReceivableAmountYuan = _.round(
          _.subtract(inTotalReceivableAmountYuan, item.remainReceivableAmountYuan as number),
          2,
        );
        return item;
      }

      item.currReceivedAmount = inTotalReceivableAmountYuan.toString();
      inTotalReceivableAmountYuan = 0;
      return item;
    });
    setReceivableList(newReceivableList);
    const currTotalReceivableAmount = _.round(
      _.subtract(oldTotalReceivableAmountYuan, inTotalReceivableAmountYuan),
      2,
    );
    setCurrTotalReceivableAmountYuan(currTotalReceivableAmount.toString());
  };

  const remarkTitleWithCount = (
    <>
      备注
      <span
        style={{
          float: 'right',
          fontWeight: 'normal',
          fontStyle: 'normal',
          color: '#666666',
        }}
      >
        {remark.length}/100
      </span>
    </>
  );

  const addPayment = async () => {
    if (!accountId) {
      showToast('尚未选择收款账户，请先选择！');
      return false;
    }
    const selectedOrderDetailList = receivableList
      .filter((item) => item.currReceivedAmount)
      .map((item) => ({
        receivableId: item.id,
        receivedAmountYuan: item.currReceivedAmount,
        ledgerType: item.ledgerType,
      }));
    if (!selectedOrderDetailList || selectedOrderDetailList.length == 0) {
      showToast('本次收款尚未选择具体的单据，请检查！');
      return false;
    }
    const totalReceivedAmount = selectedOrderDetailList.reduce((accumulator, currentItem) => {
      return _.add(accumulator, Number(currentItem.receivedAmountYuan));
    }, 0);
    if (totalReceivableAmountYuan != totalReceivedAmount) {
      showToast('各单据收款总金额与输入收款金额不一致，请修改！');
      return false;
    }
    setIsAdding(true);
    const result = await receivedConfirmation({
      buyerId: chooseCst?.base?.id ?? '',
      totalReceivedAmountYuan: totalReceivableAmountYuan,
      receivedAccountId: accountId,
      remark: remark,
      receivedAccountName: accountName,
      buyerName: chooseCst?.base?.cstName ?? '',
      ledgerType: Number(totalReceivableAmountYuan) > 0 ? 2 : 1,
      finReceivedOrderDetailCmdList: selectedOrderDetailList,
    });
    if (result) {
      Taro.showToast({
        title: '新增收款操作成功！',
        icon: 'none',
        duration: 2000,
      }).then(() => Taro.navigateBack());
    }
    setIsAdding(false);
  };

  return (
    <div className="flex flex-col h-screen">
      <CustomNavBar title="新增收款" />

      <div className="flex-1 min-h-0 overflow-y-auto">
        <CstCard
          className="mt-[24px]"
          onConfirm={setChooseCst}
          cstId={chooseCst?.base?.id ?? ''}
          isFin={true}
        />

        <Card title={
          <div className="flex align-center text-[32px] font-medium text-[#111111] pb-[8px] pt-[28px]">
            <span>
              收款金额
            </span>
            <span className='text-primary font-normal text-[28px] ml-1 leading-[1.1]'>
              手机端收款和核销只支持澳元币种
            </span>
          </div>
        }>
          <div className="flex grow text-[#666666] pt-[8px] justify-between">
            <Input
              className="w-1/2"
              style={{ '--nutui-input-font-size': '24px', '--nutui-input-padding': '10px 0px' }}
              value={totalReceivableAmountYuan}
              placeholder="0.00"
              onChange={(input) => {
                if (!checkAmount(input)) {
                  return;
                }
                setTotalReceivableAmountYuan(input);
              }}
            />
            <div className="flex justify-between items-center">
              <div></div>
              <div className="text-[32px] mr-[10px]" onClick={() => setVisibleAccount(true)}>
                {accountName ?? '收款账户'}
              </div>
              <div className="flex items-center" onClick={() => setVisibleAccount(true)}>
                <ArrowDown className={classNames('self-center', { 'opacity-[0.5]': !accountId })} />
              </div>
            </div>
          </div>
          <div className='mb-2'>
            <div className='text-[#********]'>
              调整金额
            </div>
            <Radio.Group direction="horizontal"
              value={adjustType}
              onChange={(v) => setAdjustType(v)}
              className='mt-1'
              style={{
                '--nutui-radio-button-border-radius': '4px',
                '--nutui-radio-button-background': '#080D0D0D',
                '--nutui-radio-button-active-border': '#fdf5e8',
                '--nutui-radiogroup-radio-margin': '10px',
                '--nutui-radio-button-padding': '8px 20px'
              }}>
              <Radio value={AdjustType.NONE} shape="button">无</Radio>
              <Radio value={AdjustType.ROUND} shape="button">整单抹零</Radio>
              <Radio value={AdjustType.DISCOUNT} shape="button">收款优惠</Radio>
            </Radio.Group>
            <div className='flex gap-2 w-full justify-between align-center py-2'>
              <span className='text-[##000000E]'>
                调整金额
              </span>
              <Input style={{
                padding: '0px',
              }} align="right" />
            </div>
            <Divider />
            <div className='flex gap-2 w-full justify-between align-center py-2'>
              <span className='text-[##000000E]'>
                调整原因
              </span>
              <Input style={{
                padding: '0px',
              }} align="right" />
            </div>
            <Divider />
          </div>
          <Button className="text-[32px] w-full" onClick={() => autoAssignOrders()}>
            自动分配
          </Button>
        </Card>

        {chooseCst && (
          <>
            <Card title="核销订单" className="my-0">
              {receivableList.map((item) => (
                <div>
                  <div className="flex justify-start py-[24px]">
                    <div className="px-[20px] flex flex-col flex-1">
                      <div className=" text-[32px] font-medium text-[#111111]">{item.orderNo}</div>
                      <div className="flex grow text-[#666666] pt-[8px] justify-between">
                        <div className="text-[28px] w-1/2">{item.storeName}</div>
                        <div className="text-[28px] w-1/2">{item.billDate}</div>
                      </div>
                      <div className="flex  items-center grow text-[28px] text-[#666666] pt-[8px] justify-between">
                        <div className="w-1/2 whitespace-nowrap">
                          未收：
                          <Price
                            price={item?.remainReceivableAmountYuan ?? 0}
                            size="normal"
                            style={{ color: '#666666' }}
                          />
                        </div>
                        <div className="flex items-center w-1/2 justify-between">
                          <span className="w-1/2">本次核销</span>
                          <Input
                            type="text"
                            style={{
                              '--nutui-input-font-size': '14',
                              '--nutui-input-padding': '0px',
                              '--nutui-input-border-bottom-width': '1px',
                            }}
                            placeholder="0.00"
                            value={item.currReceivedAmount}
                            onChange={(input) => {
                              if (!checkAmount(input)) {
                                return;
                              }
                              handleUpdate(item.id, input);
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <Divider />
                </div>
              ))}
            </Card>

            <Card title={remarkTitleWithCount} style={{ display: 'flex', flexDirection: 'column' }}>
              <TextArea
                onChange={(e) => setRemark(e)}
                value={remark ?? ''}
                placeholder="请输入备注信息"
                maxLength={100}
                autoSize={false}
                style={{ height: '40px', '--nutui-textarea-padding': '0px' }}
              />
            </Card>
          </>
        )}
      </div>

      {chooseCst &&
        <div className="empty:hidden">
          {Number(totalReceivableAmountYuan ?? 0) !=
            Number(currTotalReceivableAmountYuan ?? 0) && (
              <div className="justify-between payment-info">
                <IconFont name={infoIcon} className="inline-block w-auto h-auto" />
                <span className="inline-block text-block">本次核销合计需等于收款金额</span>
              </div>
            )}

          <div className="justify-between bg-white">
            <div className="flex justify-between items-center h-[150px]">
              <div className="flex flex-col items-start px-[28px] w-2/3">
                <div className="flex flex-grow">
                  <span className="opacity-60">核销合计</span>
                  <Price
                    price={currTotalReceivableAmountYuan ?? 0}
                    size="large"
                    style={{ fontWeight: 'bold' }}
                  />
                </div>
                <div className="flex flex-grow">
                  <span className="opacity-45">收款金额</span>
                  <Price
                    price={totalReceivableAmountYuan ?? 0}
                    size="normal"
                    style={{ color: 'rgba(0,0,0,0.45)' }}
                  />
                </div>
              </div>
              <Space className="px-[28px]">
                <Button loading={isAdding} type="primary" onClick={() => addPayment()}>
                  确定
                </Button>
              </Space>
            </div>
          </div>
        </div>
      }

      <AccountSelectModal
        title="选择收款账户"
        handleClose={() => setVisibleAccount(false)}
        handleConfirm={(option: { title: string; value: string }) => {
          setAccountId(option.value);
          setAccountName(option.title);
          setVisibleAccount(false);
        }}
        visibleAccount={visibleAccount}
        accountId={accountId}
      />
    </div>
  );
}
