import { ReceivedEntity } from "@/packages/finance/receive/list/types/ReceivedEntity";
import {
  Divider,
  Price,
  Tag,
} from '@nutui/nutui-react-taro';
import { navigateTo } from '@tarojs/taro';
import { ReceivedStatusEnum } from "../../list/types/ReceivedEntityEnum";

export interface Index {
  itemId: string;
  price: number;
  number: number;
}

export interface ReceiveItemProps {
  record: ReceivedEntity;
}

const ReceiveItem = (props: ReceiveItemProps) => {
  const { record } = props;

  return (
    <div
      className="bg-white rounded-[16px] mb-[24px] mx-[28px] px-[28px] py-[20px]"
      onClick={() => navigateTo({ url: '/packages/finance/receive/detail/index?receiveSerialNumber=' + record.serialNumber })}
    >
      <div className="flex justify-between pb-[16px]">
        <span>{record.buyerName}</span>
        <Tag
          type={ReceivedStatusEnum[record.status].status}
          color={ReceivedStatusEnum[record.status].color}
        >{ReceivedStatusEnum[record.status].text}</Tag>
      </div>
      <Divider />
      <div className="flex mt-[24px]">
        <div className="flex-1 text-secondary text-[28px] leading-6">
          <div>
            <span className="inline-block w-[150px]">
              收款单号
            </span>
            {record.serialNumber}</div>
          <div>
            <span className="inline-block w-[150px]">
              收款时间
            </span>
            {record.businessTime}
          </div>
          <div>
            <span className="inline-block w-[150px]">
              收款门店
            </span>
            {record.storeName}</div>
          <div>
            <span className="inline-block w-[150px]">
              制单人
            </span>
            {record.createPerson}
          </div>
        </div>
        <div className="flex flex-col justify-center items-end">
          <div>
            <Price symbol="" price={record.totalReceivedAmountYuan?.toString()} size="normal" style={{ color: 'rgba(0,0,0,0.9)' }} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReceiveItem;
