import '@/assets/lightPage.scss';
import CalendarRangeCardChoose from '@/components/CalendarRangeCardChoose';
import { queryStore } from '@/components/ChooseStoreAndWarehouseModal/services';
import CustomMultipleChoose from '@/components/CustomMultipleChoose';
import CustomNavBar from "@/components/CustomNavBar";
import FilterCustomerPicker from '@/components/FilterCustomerPicker';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import ReceiveItem from '@/packages/finance/receive/components/receiveItem';
import { queryReceivedPage } from '@/packages/finance/receive/services';
import { QueryPostListRequest } from '@/packages/stocks/output/list/types/query.post.list.request';
import { Add } from '@nutui/icons-react-taro';
import { Menu, SafeArea } from '@nutui/nutui-react-taro';
import { navigateTo, useDidShow } from '@tarojs/taro';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { ReceivedEntity } from './types/ReceivedEntity';

export default function Index() {
  const calendarItemRef = useRef<any>(null);
  const [params, setParams] = useState<any>({});
  const cstMenuRef = useRef<any>();
  const storeRef = useRef<any>();
  const [onLoadFlag, setOnLoadFlag] = useState<boolean>(false);
  const [storeList, setStoreList] = useState<any>([]);

  useDidShow(() => {
    if (onLoadFlag) {
      setParams((prevData) => ({ ...prevData, time: _.now() }));
    }
  });

  useEffect(() => {
    queryStore({}).then((result) => setStoreList(result));
  }, []);

  /**
   * 列表查询
   * @param param
   */
  const fetchData = (param?: QueryPostListRequest): Promise<ReceivedEntity[]> => {
    return queryReceivedPage({
      ...param,
      pageSize: 10,
    }).then((result) => result?.data ?? []);
  };

  return (
    <div className="pt-[16px] flex flex-col h-screen">
      <CustomNavBar title="收款" showBack={true} />
      <div className="flex justify-between">
        <MenuWrap
          menu={
            <Menu>
              <Menu.Item title="收款时间" ref={calendarItemRef}>
                <CalendarRangeCardChoose
                  value={
                    params.startBusinessTime
                      ? [
                        dayjs(params.startBusinessTime).toDate(),
                        dayjs(params.endBusinessTime).toDate(),
                      ]
                      : []
                  }
                  onChange={(value) => {
                    if (value) {
                      setParams({
                        ...params,
                        startBusinessTime: value[0]
                          ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00')
                          : undefined,
                        endBusinessTime: value[1]
                          ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59')
                          : undefined,
                      });
                      calendarItemRef.current?.toggle(false);
                    }
                  }}
                />
              </Menu.Item>
              <Menu.Item title="客户" ref={cstMenuRef}>
                <FilterCustomerPicker
                  cstId={params.buyerId}
                  onChange={(buyerId) => {
                    setParams({ ...params, buyerId: buyerId === '' ? null : buyerId });
                    cstMenuRef.current?.toggle(false);
                  }}
                />
              </Menu.Item>
              <Menu.Item title="收款门店" ref={storeRef}>
                <CustomMultipleChoose
                  key={'storeIdList'}
                  onClose={() => {
                    storeRef.current?.toggle(false);
                  }}
                  selected={{
                    storeIdList: params.storeIdList ?? [],
                  }}
                  onConfirm={(e) => {
                    setParams({ ...params, ...e });
                  }}
                  items={[{
                    label: '',
                    keyStr: 'storeIdList',
                    multiple: true,
                    item: storeList.map((item) => ({ text: item.name, value: item.id })),
                  }]}
                />
              </Menu.Item>
            </Menu>
          }
        />
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {/* <PermissionComponent permission={'addReceive'}> */}
          <Add
            className="mx-[28px]"
            onClick={() => {
              navigateTo({ url: '/packages/finance/receive/add/index' });
              setOnLoadFlag(true);
            }}
          />
          {/* </PermissionComponent> */}
        </div>
      </div>
      <div className="flex-1 min-h-0 overflow-scroll mt-[24px]">
        <FunPagination
          fetchData={fetchData}
          params={params}
          renderItem={(record) => <ReceiveItem record={record} />}
        />
      </div>
      <SafeArea position={'bottom'} />
    </div>
  );
}
