
export enum ReceivedStatus {
    /**
     * 已取消
     */
    CANCELLED = -2,
    /**
     * 审核不通过
     */
    ABORTED = -1,
    /**
     * 草稿
     */
    DRAFT = 0,
    /**
    /**
     * 待审核
     */
    PENDING = 1,
    /**
     * 待支付
     */
    UNPAYMENT = 2,
    /**
     * 已收款
     */
    PAYMENT = 3,
}

export enum ReceivedType {
    /**
     * 收入
     */
    INCOME = 1,
    /**
     * 支出
     */
    EXPENSE = 2,
}

export const ReceivedStatusEnum = {
    [ReceivedStatus.CANCELLED]: { text: '已取消', status: 'danger', color: 'red' },
    [ReceivedStatus.ABORTED]: { text: '审核不通过', status: 'danger', color: 'red' },
    [ReceivedStatus.DRAFT]: { text: '草稿', status: 'primary', color: 'orange' },
    [ReceivedStatus.PENDING]: { text: '待审核', status: 'primary', color: 'orange' },
    [ReceivedStatus.UNPAYMENT]: { text: '待支付', status: 'primary', color: 'orange' },
    [ReceivedStatus.PAYMENT]: { text: '已收款', status: 'success', color: 'green' },
};

export enum AdjustType {
    /**
     * 无
     */
    NONE = 1,
    /**
     * 收款抹零
     */
    ROUND = 2,
    /**
     * 收款优惠
     */
    DISCOUNT = 3,
}

// export const ReceivedTypeEnum = {
//   [ReceivedType.INCOME]: { text: '收入', status: 'Success' },
//   [ReceivedType.EXPENSE]: { text: '支出', status: 'Error' },
// };