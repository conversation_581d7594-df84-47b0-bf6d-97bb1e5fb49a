import { find, findIndex, map } from 'lodash';
import { create } from 'zustand';
import { CustomerAddressEntity } from '../../list/types/CustomerAddressEntity';
import { CustomerContactEntity } from '../../list/types/CustomerContactEntity';
import { CustomerImageEntity } from '../../list/types/CustomerImageEntiry';
import { CustomerSaveEntity } from '../../list/types/CustomerSaveEntity';
import { CustomerTagEntity } from '../../list/types/CustomerTagEntity';

/**定义状态 */
export type CustomerAddState = {
    // 是否需要重刷新页面
    needReload: boolean,
    tagIdList?: string[];
    tagNames?: string;
    images?: CustomerImageEntity[],
    cstStatus: number,


    // 结算信息
    orgReceivableAmount?: string;
    totalAmount?: string;
    credit?: boolean;
    creditTerms?: string;
    tags?: CustomerTagEntity[];
    contacts: CustomerContactEntity[];
    addresses: CustomerAddressEntity[];
} & Pick<CustomerSaveEntity, 'settle' | 'base'>;
/**
 * 定义行为
 */
interface CustomerAddActions {
    /**
     * 新增或保存
     * @param record CustomerContactEntity
     * @returns
     */
    updateContact: (record: CustomerContactEntity) => void;
    // 删除现款账户配置
    removeContact: (id: string) => void;
    /**
     * 新增或保存
     * @param record CustomerAddressEntity
     * @returns
     */
    updateAddress: (record: CustomerAddressEntity) => void;
    // 删除现款账户配置
    removeAddress: (id: string) => void;
    setState: <K extends keyof CustomerAddState>(key: K, value: CustomerAddState[K]) => void;
    reset: () => boolean,
}
/**定义初始值 */
const initialState = {
    needReload: false,
    baseId: undefined,
    tagIdList: undefined,
    tags: undefined,
    tagNames: undefined,
    storeId: undefined,
    storeName: undefined,
    salesmanId: undefined,
    salesmanName: undefined,
    cstStatus: 0,
    remark: undefined,
    // 结算信息
    settle: {},
    images: undefined,
    contacts: [],
    addresses: [],
}

// 销售退货
const useCustomerAddStore = create<CustomerAddState & CustomerAddActions>((set, get) => ({
    ...initialState,
    setState: (key, value) => { set({ [key]: value }) },
    reset: () => {
        set(initialState)
        return true;
    },

    updateContact: (record) => {
        set((state) => {
            const id = record.id;
            const isDefault = record?.isDefault ?? 0;
            let contacts = [...state.contacts];
            if (!find(contacts, { id })) {
                contacts = [...state.contacts, record]
            }
            const array = map(contacts, r => {
                if (r.id == id) {
                    return { ...r, ...record }
                }
                return { ...r, isDefault: isDefault == 1 ? 0 : r.isDefault }
            })
            return { contacts: array };
        });
    },
    updateAddress: (record) => {
        set((state) => {
            const id = record.id;
            const isDefault = record?.isDefault ?? 0;
            let addresses = [...state.addresses];
            if (!find(addresses, { id })) {
                addresses = [...state.addresses, record]
            }
            const array = map(addresses, r => {
                if (r.id == id) {
                    return { ...r, ...record }
                }
                return { ...r, isDefault: isDefault == 1 ? 0 : r.isDefault }
            })
            return { addresses: array };
        });
    },
    removeAddress: (id) => {
        set((state) => {
            const addresses = [...state.addresses!];
            const index = findIndex(addresses, { id })
            addresses.splice(index, 1);
            return { addresses };
        });
    },
    removeContact: (id) => {
        set((state) => {
            const contacts = [...state.contacts!];
            const index = findIndex(contacts, { id })
            contacts.splice(index, 1);
            return { contacts };
        });
    },
}));
export default useCustomerAddStore;
