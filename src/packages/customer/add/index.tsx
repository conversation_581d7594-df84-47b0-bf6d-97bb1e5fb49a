import iconCustDef from '@/assets/icons/icon_cust_def.png';
import iconEditor from '@/assets/icons/icon_editor.png';
import CustomPicker from '@/components/CustomPicker';
import CustomerTagPicker from '@/components/CustomerTagPicker';
import { DefautlOptionType } from '@/packages/sales/returns/operation/types/DefaultOptionType';
import RouterUtils from '@/utils/RouterUtils';
import { Add, ArrowRight, MaskClose } from '@nutui/icons-react-taro';
import {
  ConfigProvider,
  Image,
  ImagePreview,
  Input,
  SafeArea,
  Switch,
  Tag,
  TextArea
} from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro, { useLoad, useRouter, useUnload } from '@tarojs/taro';
import { useAsyncEffect } from 'ahooks';
import { filter, find, includes, isEmpty, map, size } from 'lodash';
import { useState } from 'react';
import './index.scss';

import CustomNavBar from "@/components/CustomNavBar";
import { CustomerStatusOptions, DeliveryAmountTypeOptions } from '@/packages/customer/detail/config/customerOptions';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import ArrayUtils from '@/utils/ArrayUtils';
import { uploadImage } from "@/utils/uploadImage";
import { validateForm } from '@/utils/validatorUtils';
import ContactItem from '../detail/components/ContactItem';
import { getCstDetail, saveCst } from '../list/services';
import { Base, DeliveryAmountType } from '../list/types/CustomerSaveEntity';
import { CustomerTagEntity } from '../list/types/CustomerTagEntity';
import SettleInfo from './components/SettleInfo';
import { getTagList, queryStore, queryStoreAccount } from './services';
import useCustomerAddStore from './store';
export interface FormItemType<T> {
  key: string;
  label: string;
  placeholder?: string;
  name: keyof T;
  defaultValue?: string;
  value?: string;
  type?: string;
  required?: boolean;
  onClick?: () => void;
  valueEnum?: Record<string, any>;
}

export default () => {
  const store = useCustomerAddStore();
  const { base } = store;
  const { params } = useRouter();
  useLoad(() => {
    let title = '';
    if (params.cstId) {
      title = '编辑客户';
    } else {
      title = '新增客户';
    }
    Taro.setNavigationBarTitle({ title });
  });
  useUnload(() => {
    console.log('reset', store.reset());
  });

  const setBaseState = (key, value) => {
    store.setState('base', {
      ...(store.base ?? {}),
      [key]: value,
    });
  }

  useAsyncEffect(async () => {
    if (isEmpty(params.cstId)) return;
    const result = await getCstDetail({ cstId: params.cstId! });
    const { base, contacts, tags, images, settle, addresses } = result;
    if (addresses && ArrayUtils.isNotEmpty(addresses)) {
      store.setState('addresses', addresses);
    }
    if (contacts && ArrayUtils.isNotEmpty(contacts)) {
      store.setState('contacts', contacts.map((t) => ({ ...t, positions: t.position?.split(',') ?? [] })));
    }
    if (tags && ArrayUtils.isNotEmpty(tags)) {
      let tagIds = tags.map((t) => t.tagId ?? '');
      let tagNames = tags.map((t) => t.tagName ?? '').join(',');
      store.setState('tags', tags);
      store.setState('tagIdList', tagIds);
      store.setState('tagNames', tagNames);
    }
    if (base) {
      store.setState('base', base);
    }
    if (settle) {
      store.setState('settle', settle);
    }
    if (images && ArrayUtils.isNotEmpty(images)) {
      const image = images[0];
      setTmpImageUrl(image.url);
      store.setState('images', images);
    }
  }, [params.cstId]);

  const [showPreview, setShowPreview] = useState(false);
  // 是否点击过归属门店
  const [isClick, setIsClick] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [visibleMap, setVisibleMap] = useState<Record<string, boolean>>({
    storeVisible: false,
    tagVisible: false,
    salesManVisible: false,
    statusVisible: false,
    deliveryAmountTypeVisible: false,
  });

  // 加载门店列表
  const [storeList, setStoreList] = useState<DefautlOptionType[]>([]);

  // 加载客户标签列表
  useAsyncEffect(async () => {
    const data = await queryStore();
    if (data) {
      const accList = data.map((item) => ({
        title: item.name,
        value: item.id,
      }));
      setStoreList(accList);
    }
  }, []);
  // 加载业务员列表
  const [salesManList, setSalesManList] = useState<DefautlOptionType[]>([]);
  useAsyncEffect(async () => {
    const storeId = store.storeId;
    if (storeId) {
      if (isClick) {
        store.setState('salesmanName', undefined);
        store.setState('salesmanId', undefined);
        setSalesManList([]);
      }
      const data = await queryStoreAccount(storeId);
      if (data) {
        const salesManList = data.map((item) => ({
          title: item.name,
          value: item.id,
        }));
        setSalesManList(salesManList);
      }
    }
  }, [store.storeId]);
  const [tagList, setTagList] = useState<DefautlOptionType[]>([]);
  useAsyncEffect(async () => {
    const data = await getTagList();
    if (data) {
      const tagList = data.map((item) => ({
        title: item.tagName ?? '',
        value: item.id ?? '',
      }));
      setTagList(tagList);
    }
  }, []);
  const FormItems: FormItemType<Base>[] = [
    {
      key: 'cstName',
      label: '客户名称',
      name: 'cstName',
      required: true,
      value: base?.cstName,
    },
    { key: 'cstSn', label: '客户编码', name: 'cstSn', value: base?.cstSn },
    { key: 'nickName', label: '客户简称', name: 'nickName', value: base?.nickName },
    { key: 'abn', label: 'ABN', name: 'abn', value: base?.abn, required: true },
    {
      key: 'cstStatus',
      label: '客户状态',
      placeholder: '请选择',
      name: 'cstStatus',
      value: CustomerStatusOptions.find((o) => o.value === base?.cstStatus)?.title,
      type: 'select',
      onClick: () => setVisibleMap((pre) => ({ ...pre, statusVisible: true })),
    },
    {
      key: 'storeName',
      label: '归属门店',
      placeholder: '请选择',
      name: 'storeName',
      value: base?.storeName,
      type: 'select',
      onClick: () => {
        setIsClick(true);
        setVisibleMap((pre) => ({ ...pre, storeVisible: true }));
      },
    },
    {
      key: 'salesmanName',
      label: '业务员',
      placeholder: '请选择',
      name: 'salesmanName',
      value: base?.salesmanName,
      type: 'select',
      onClick: () => {
        if (!base?.storeId) {
          Taro.showToast({ icon: 'none', title: '请先选择归属门店' });
          return;
        }
        setVisibleMap((pre) => ({ ...pre, salesManVisible: true }));
      },
    },
    {
      key: 'tagNames',
      label: '客户标签',
      placeholder: '请选择',
      name: 'tagNames',
      value: store?.tagNames,
      type: 'select',
      onClick: () => {
        setVisibleMap((pre) => ({ ...pre, tagVisible: true }));
      },
    },
    { key: 'universalEmail', label: '通用邮箱', name: 'universalEmail', value: base?.universalEmail },
    { key: 'financeEmail', label: '财务邮箱', name: 'financeEmail', value: base?.financeEmail },
    { key: 'sendFinanceEmailFlag', label: '发送财务邮箱', name: 'sendFinanceEmailFlag', value: base?.sendFinanceEmailFlag, type: 'switch' },
    {
      key: 'deliveryAmountType',
      label: '运费类型',
      name: 'deliveryAmountType',
      placeholder: '请选择',
      value: DeliveryAmountTypeOptions.find((o) => o.value === base?.deliveryAmountType)?.title,
      type: 'select',
      onClick: () => {
        setVisibleMap((pre) => ({ ...pre, deliveryAmountTypeVisible: true }))
        setBaseState('deliveryAmount', null);
      },
    },
    { key: 'deliveryAmount', label: '运费', name: 'deliveryAmount', value: base?.deliveryAmount },
  ].filter(item => {
    if (item.key === 'deliveryAmount') {
      return base?.deliveryAmountType === DeliveryAmountType.Fixed;
    }
    return true;
  });

  // 新增保存
  const handleConfirm = async () => {
    const valid = validateForm(FormItems);
    if (!valid) return;
    const result = await saveCst({
      base: {
        ...store.base,
      },
      settle: {
        ...store.settle,
      },
      addresses: store.addresses.map((t) => ({
        ...t,
        id: t?.id?.startsWith('address_') ? undefined : t.id,
      })),
      contacts: store.contacts.map((t) => ({
        ...t,
        id: t?.id?.startsWith('contact_') ? undefined : t.id,
      })),
      images: store.images,
      tags: store.tags,
    });
    if (result) {
      Taro.showToast({ icon: 'success', title: '操作成功' });
      store.setState('needReload', true);
      Taro.navigateBack();
    }
  };

  const [tmpImageUrl, setTmpImageUrl] = useState<string>(iconCustDef);
  const upload = () => {
    setLoading(true);
    uploadImage().then(result => {
      store.setState('images', [{ url: result }]);
      setTmpImageUrl(result);
    }).finally(() => {
      setLoading(false);
    });
  };

  return (
    <ConfigProvider
      theme={{
        nutuiInputPadding: '8px 0px',
        nutuiTextareaPadding: '0px 0px',
        nutuiCollapseItemPadding: '8px 8px',
        nutuiCollapseWrapperContentPadding: '8px 8px',
        nutuiCollapseItemLineHeight: '14px',
        nutuiCollapseItemColor: '#000000E6',
      }}
    >
      <CustomNavBar title={params.cstId ? '编辑客户' : '新增客户'} showBack={true} />
      <div className="p-[28px] flex flex-col gap-[24px] pb-[200px] text-main">
        <div
          id="customerAddForm"
          className="px-[28px] pt-[28px] rounded-[16px] bg-white flex flex-col"
        >
          <div className="flex justify-between items-center">
            <div className="relative">
              <Image
                onClick={(e) => {
                  e.stopPropagation();
                  if (store.images) {
                    setShowPreview(true);
                  }
                }}
                src={tmpImageUrl}
                loading={loading}
                width="50px"
                height="50px"
              ></Image>
              {store.images && (
                <span
                  onClick={(e) => {
                    e.stopPropagation();
                    setTmpImageUrl(iconCustDef);
                    store.setState('images', undefined);
                  }}
                  className="absolute top-[-20px] right-[-20px]"
                >
                  <MaskClose size={16} color="#F83431" />
                </span>
              )}
            </div>
            <div className="flex items-center gap-[12px]" onClick={upload}>
              <span className="text-[#0000004D] text-[32px]">请选择</span>
              <ArrowRight color="#0000004D" height="26px" width="16px"></ArrowRight>
            </div>
          </div>
          {FormItems.map((formItem) => {
            const {
              key,
              name,
              label,
              placeholder = '请输入',
              value,
              type = 'input',
              required,
              onClick,
            } = formItem;
            return (
              <View key={key} className="formItem">
                <div className="flex justify-between items-center">
                  <span className={`text-[32px] text-main  ${required && 'formItemRequired'}`}>
                    {label}
                  </span>
                  <span className="flex items-center gap-[12px]" onClick={onClick}>
                    {type == 'switch' && <div className='py-2'>
                      <Switch checked={value} onChange={(value) => {
                        setBaseState(name, value);
                      }} />
                    </div>}
                    {type == 'input' && <Input
                      name={name}
                      placeholder={placeholder}
                      placeholderClass="text-[#0000004D] text-[32px]"
                      align="right"
                      value={value}
                      onChange={(value) => {
                        setBaseState(name, value);
                      }}
                    />}
                    {type == 'select' && (
                      <>
                        <Input
                          name={name}
                          disabled
                          placeholder={placeholder}
                          placeholderClass="text-[#0000004D] text-[32px]"
                          align="right"
                          value={value}
                        />
                        <ArrowRight color="#0000004D" height="26px" width="16px"></ArrowRight>
                      </>
                    )}
                  </span>
                </div>
              </View>
            );
          })}
        </div>
        <div className="p-[28px] rounded-[16px] bg-white flex flex-col">
          <div className="text-[32px] mb-[32px] font-medium">联系人信息</div>
          <div id="customerContactList" className="flex flex-col gap-[24px]">
            {store.contacts &&
              store.contacts.map((contact) => {
                const { positionList = [] } = contact;
                return (
                  <View key={contact.id} className="listItem">
                    <div className="flex flex-col gap-[28px]">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-[16px]">
                          <span className="text-main text-[32px]">{contact.firstName} {contact.lastName}</span>
                          {contact.isDefault == 1 && (
                            <span className="px-[12px] py-[6px] bg-[#FFF4F4] text-primary text-[20px] rounded-[4px] border-[#FCAEADFF] border-solid border-[1px]">
                              默认联系人
                            </span>
                          )}
                          {
                            positionList?.slice(0, 2)?.map((position, index) => (
                              <Tag key={index} type="info">
                                {position.positionName}
                              </Tag>
                            ))
                          }
                          {
                            positionList?.length > 2 && (
                              <Tag type="info">
                                +{positionList?.length - 2}
                              </Tag>
                            )
                          }
                        </div>
                        <Image
                          onClick={(e) => {
                            e.stopPropagation();
                            RouterUtils.navigateTo({
                              url: '/packages/customer/contacts/add/index',
                              params: { id: contact.id },
                            });
                          }}
                          src={iconEditor}
                          width="16px"
                          height="16px"
                        ></Image>
                      </div>
                      <ContactItem record={contact}></ContactItem>
                    </div>
                  </View>
                );
              })}
          </div>
          <div
            onClick={() => {
              RouterUtils.navigateTo({ url: '/packages/customer/contacts/add/index' });
            }}
            className="mt-[24px] flex justify-center items-center gap-[16px]"
          >
            <Add color="#F49C1FFF" height="14px" width="14px" />
            <span className="text-primary text-[28px] w-[150px]">新增联系人</span>
          </div>
        </div>
        <div className="p-[28px] rounded-[16px] bg-white flex flex-col">
          <div className="text-[32px] mb-[32px] font-medium">地址信息</div>
          <div id="customerAddressList" className="flex flex-col gap-[24px]">
            {store?.addresses &&
              store.addresses.map((address) => {
                return (
                  <View className="listItem">
                    <div className="flex flex-col gap-[24px]">
                      <div className="flex justify-between text-[32px] text-main items-center">
                        <div className="flex items-center gap-[16px]">
                          <span>{address.firstName} {address.lastName}</span>
                          <span>{address.phone}</span>
                          {address.isDefault == 1 && (
                            <span className="px-[12px] py-[6px] bg-[#FFF4F4] text-primary text-[20px] rounded-[4px] border-[#FCAEADFF] border-solid border-[1px]">
                              默认地址
                            </span>
                          )}
                        </div>
                        <Image
                          onClick={(e) => {
                            e.stopPropagation();
                            RouterUtils.navigateTo({
                              url: '/packages/customer/address/add/index',
                              params: { id: address.id },
                            });
                          }}
                          src={iconEditor}
                          width="16px"
                          height="16px"
                        ></Image>
                      </div>
                      <span className="text-[28px] text-[#777777]">
                        {address.provinceName}
                        {address.cityName}
                        {address.prefectureName}
                        {address.address}
                      </span>
                    </div>
                  </View>
                );
              })}
          </div>
          <div
            onClick={() => {
              RouterUtils.navigateTo({ url: '/packages/customer/address/add/index' });
            }}
            className="mt-[24px] flex justify-center items-center gap-[16px]"
          >
            <Add color="#F49C1F" height="14px" width="14px" />
            <span className="text-primary text-[28px] w-[150px]">新增地址</span>
          </div>
        </div>

        <SettleInfo />

        <div className="p-[28px] rounded-[16px] bg-white">
          <div className="flex justify-between items-center">
            <span className="text-[32px] mb-[32px]">备注</span>
            <span className="text-thirdary">{size(base?.remark)}/100</span>
          </div>
          <span className="h-[200px]">
            <TextArea
              value={base?.remark}
              onChange={(value) => {
                setBaseState('remark', value);
              }}
              maxLength={100}
              style={{ height: '85px' }}
              placeholder="请输入备注信息"
            />
          </span>
        </div>
        <PermissionComponent permission="addCustomer">
          <div
            className="bg-white py-[20px] px-[28px] fixed z-50 left-0 right-0 bottom-0"
            onClick={handleConfirm}
          >
            <span className=" bg-[#F49C1F] text-[32px] text-white rounded-[8px] py-[16px] flex justify-center items-center">
              {params.cstId ? '保存' : '新增'}
            </span>
            <SafeArea position="bottom"></SafeArea>
          </div>
        </PermissionComponent>
      </div>
      <CustomPicker
        key="storeSelect"
        items={storeList}
        title="选择门店"
        needConfirm={false}
        visible={visibleMap.storeVisible}
        selected={base?.storeId ? [base.storeId] : []}
        onClose={() => {
          setVisibleMap((pre) => ({ ...pre, storeVisible: false }));
        }}
        multiple={false}
        onConfirm={(items) => {
          if (items) {
            const id = items[0];
            const storeItem = find(storeList, { value: id });
            if (storeItem) {
              setBaseState('storeName', storeItem.title);
              setBaseState('storeId', storeItem.value);
            }
          }
          setVisibleMap((pre) => ({ ...pre, storeVisible: false }));
        }}
      />
      <CustomPicker
        key="salesManSelect"
        items={salesManList}
        title="选择业务员"
        needConfirm={false}
        visible={visibleMap.salesManVisible}
        selected={base?.salesmanId ? [base.salesmanId] : []}
        onClose={() => {
          setVisibleMap((pre) => ({ ...pre, salesManVisible: false }));
        }}
        multiple={false}
        onConfirm={(items) => {
          if (items) {
            const id = items[0];
            const salesmanItem = find(salesManList, { value: id });
            if (salesmanItem) {
              setBaseState('salesmanId', salesmanItem.value);
              setBaseState('salesmanName', salesmanItem.title);
            }
          }
          setVisibleMap((pre) => ({ ...pre, salesManVisible: false }));
        }}
      />
      <CustomerTagPicker
        key="customerTagSelect"
        items={tagList}
        visible={visibleMap.tagVisible}
        selected={store?.tagIdList ?? []}
        onClose={() => {
          setVisibleMap((pre) => ({ ...pre, tagVisible: false }));
        }}
        onConfirm={(items) => {
          let idList: string[];
          let tags: CustomerTagEntity[];
          if (items) {
            const selectTags = filter(tagList, (t) => includes(items, t.value));
            store.setState('tagNames', map(selectTags, 'title').join(','));
            idList = items;
            tags = items.map((t) => ({ tagId: t }));
          } else {
            idList = [];
            tags = [];
          }
          store.setState('tags', tags);
          store.setState('tagIdList', idList);
          setVisibleMap((pre) => ({ ...pre, tagVisible: false }));
        }}
      />
      <CustomPicker
        key="statusSelect"
        items={CustomerStatusOptions.map((o: any) => ({ title: o.title, value: String(o.value) }))}
        title="选择客户状态"
        needConfirm={false}
        visible={visibleMap.statusVisible}
        selected={base?.cstStatus !== undefined ? [String(base.cstStatus)] : []}
        onClose={() => {
          setVisibleMap((pre) => ({ ...pre, statusVisible: false }));
        }}
        multiple={false}
        onConfirm={(items) => {
          if (items) {
            const id = items[0];
            setBaseState('cstStatus', Number(id));
          }
          setVisibleMap((pre) => ({ ...pre, statusVisible: false }));
        }}
      />
      <CustomPicker
        key="deliveryAmountTypeSelect"
        items={DeliveryAmountTypeOptions.map((o: any) => ({ title: o.title, value: String(o.value) }))}
        title="选择运费类型"
        needConfirm={false}
        visible={visibleMap.deliveryAmountTypeVisible}
        selected={base?.deliveryAmountType !== undefined ? [String(base.deliveryAmountType)] : []}
        onClose={() => {
          setVisibleMap((pre) => ({ ...pre, deliveryAmountTypeVisible: false }));
        }}
        multiple={false}
        onConfirm={(items) => {
          if (items) {
            const id = items[0];
            setBaseState('deliveryAmountType', Number(id));
          }
          setVisibleMap((pre) => ({ ...pre, deliveryAmountTypeVisible: false }));
        }}
      />
      {store.images && (
        <ImagePreview
          closeOnContentClick
          autoPlay={false}
          images={store.images.map((t) => ({
            src: t.url,
          }))}
          visible={showPreview}
          onClose={() => setShowPreview(false)}
        />
      )}
    </ConfigProvider>
  );
};
