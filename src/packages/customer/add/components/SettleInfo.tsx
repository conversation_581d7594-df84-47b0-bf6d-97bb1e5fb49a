import CustomPicker from '@/components/CustomPicker';
import { SettleTypeOptions } from '@/packages/customer/detail/config/customerOptions';
import { ArrowDown, ArrowRight } from '@nutui/icons-react-taro';
import { Collapse, ConfigProvider, Divider, Input, Switch } from "@nutui/nutui-react-taro";
import { useState } from 'react';
import useCustomerAddStore from '../store';

const SettleInfo = () => {
  const store = useCustomerAddStore();
  const { settle } = store;

  console.log(settle);

  const setState = (key, value) => {
    store.setState('settle', {
      ...store.settle,
      [key]: value,
    });
  }

  // 账期选择弹窗
  const [settleVisible, setSettleVisible] = useState<boolean>(false);

  return (
    <ConfigProvider
      theme={{
        '--nutui-switch-open-background-color': '#F49C1F',
      }}
    >
      <Collapse
        key="settleItem"
        defaultActiveName={['settle']}
        expandIcon={<ArrowDown color="#0000004D" height="16px" width="28px" />}
      >
        <Collapse.Item
          title={<span className="text-[32px] font-medium">结算信息</span>}
          name="settle"
        >
          <div id="settleItem" className="flex flex-col">
            <div className="flex justify-between items-center py-[32px]">
              <span className="text-[32px] text-main">允许挂账</span>
              <Switch
                checked={settle?.credit}
                onChange={(value) => {
                  console.log(value);
                  setState('credit', value);
                  if (!value) {
                    store.setState('settle', {
                      ...store.settle,
                      credit: false,
                      totalAmount: undefined,
                      creditTerms: undefined,
                    });
                  }
                }}
                style={{
                  '--nutui-switch-open-background-color': '#F49C1F',
                  '--nutui-switch-close-line-background-color': '#ebebeb',
                }}
              ></Switch>
            </div>
            <div className="flex justify-between items-center pb-[24px]">
              <div className="flex flex-col gap-[14px]">
                <span className={`text-[32px] ${settle?.credit ? 'text-main' : 'text-invalid'}`}>
                  挂账额度
                </span>
                {settle?.requestCreditLimit && <span
                  className={`text-[24px] ${settle?.credit ? 'text-thirdary' : 'text-invalid'}`}
                >
                  客户期望额度：${settle?.requestCreditLimit?.toFixed(2) ?? 0}
                </span>}
              </div>
              <Input
                disabled={!settle?.credit}
                name="totalAmount"
                placeholder="请输入"
                type="digit"
                placeholderClass={`text-[#0000004d] text-[32px] ${settle?.credit ? 'text-[#0000004d]' : 'text-invalid'
                  }`}
                align="right"
                maxLength={12}
                value={settle?.totalAmount}
                onChange={(value) => {
                  setState('totalAmount', value);
                }}
              />
            </div>
            <Divider />
            <div className="flex justify-between items-center mt-[32px] pb-[24px]">
              <div className="flex flex-col  gap-[14px]">
                <span className={`text-[32px]  ${settle?.credit ? 'text-main' : 'text-invalid'}`}>
                  账期
                </span>
                <span
                  className={`text-[24px]  ${settle?.credit ? 'text-thirdary' : 'text-invalid'}`}
                >
                  客户期望账期：{settle?.requestSettleType ? settle?.requestSettleType : 'Weekly'}
                </span>
              </div>
              <div
                className={`flex items-center gap-[8px] ${settle?.credit ? 'text-thirdary' : 'text-invalid'}`}
                onClick={() => {
                  if (!settle?.credit) return;
                  setSettleVisible(true);
                }}
              >
                {settle?.requestSettleType}
                <ArrowRight />
              </div>

            </div>
            <Divider />
            <div className="flex justify-between items-center py-[32px]">
              <span className="text-[32px] text-main">多币种客户</span>
              <Switch
                checked={Boolean(settle?.isMultiCurrency)}
                onChange={(value) => {
                  setState('isMultiCurrency', value ? 1 : 0);
                }}
              ></Switch>
            </div>
            <Divider />
            <div className="flex justify-between items-center py-[32px]">
              <span className="text-[32px] text-main">GST Excluded</span>
              <Switch
                checked={Boolean(settle?.gstExcluded)}
                onChange={(value) => {
                  setState('gstExcluded', value ? 1 : 0);
                }}
              ></Switch>
            </div>
          </div>
        </Collapse.Item>
      </Collapse>
      {/* 账期选择 */}
      <CustomPicker
        key="settleTypeSelect"
        items={SettleTypeOptions}
        title="选择账期"
        needConfirm={false}
        multiple={false}
        visible={settleVisible}
        selected={store?.settle?.requestSettleType ? [store.settle.requestSettleType] : []}
        onClose={() => setSettleVisible(false)}
        onConfirm={(items) => {
          if (items && items.length > 0) {
            const value = items[0];
            setState('requestSettleType', value);
          }
          setSettleVisible(false);
        }}
      />
    </ConfigProvider >)
}
export default SettleInfo