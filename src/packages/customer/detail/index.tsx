import iconCall from '@/assets/icons/icon_call.png';
import iconCustDef from '@/assets/icons/icon_cust_def.png';
import CustomNavBar from '@/components/CustomNavBar';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import RouterUtils from '@/utils/RouterUtils';
import { ArrowDown, ArrowUp, Mail, Notepad, Phone } from '@nutui/icons-react-taro';
import { Divider, Image, SafeArea, Tag } from '@nutui/nutui-react-taro';
import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import { defaultTo, isEmpty } from 'lodash';
import { useState } from 'react';
import { getCstDetail } from '../list/services';
import {
  CustomerSaveEntity,
  CustomerStatus,
  DeliveryAmountType,
} from '../list/types/CustomerSaveEntity';
import { CustomerStatusEnum, DeliveryAmountTypeEnum } from './config/customerOptions';
import './index.scss';

// 拨打电话
const makePhoneCall = (phoneNumber: string) => {
  Taro.makePhoneCall({
    phoneNumber: phoneNumber,
  });
};

export default () => {
  const { params } = useRouter();
  const { cstId } = params;
  const [detail, setDetail] = useState<CustomerSaveEntity>({});
  const { base, tags } = detail;
  const imageUrl = defaultTo(detail?.images?.[0]?.url, iconCustDef);

  // 折叠状态管理
  const [contactsExpanded, setContactsExpanded] = useState(false);
  const [addressesExpanded, setAddressesExpanded] = useState(false);
  const [settleExpanded, setSettleExpanded] = useState(false);

  useDidShow(async () => {
    const result = await getCstDetail({ cstId: cstId! });
    setDetail(result);
  });

  return (
    <div className="min-h-screen">
      <CustomNavBar title="客户详情" showBack={true} />

      {/* 客户基本信息卡片 */}
      <div className="mx-[28px] mt-[24px] p-[28px] relative">
        {/* 客户头像和基本信息 */}
        <div className="flex items-start gap-[24px]">
          {/* 客户头像 */}
          <div className="w-[120px] h-[120px] rounded-[12px] flex items-center justify-center">
            <span className="text-white text-[32px] font-medium">
              <Image src={imageUrl} width={60} height={60} />
            </span>
          </div>

          {/* 客户信息 */}
          <div className="flex-1">
            <div className="flex items-center gap-[16px] mb-[12px]">
              <span className="text-[36px] font-medium text-[#333]">
                {detail.base?.cstName || '-'}
              </span>
            </div>

            <div className="text-[28px] text-[#666] mb-[8px]">
              {detail.base?.cstSn || '-'} | {detail.base?.nickName || '-'}
            </div>

            <div className="flex flex-wrap gap-[12px] mt-[16px]">
              {
                <Tag type={CustomerStatusEnum[base?.cstStatus as CustomerStatus]?.status}>
                  {CustomerStatusEnum[base?.cstStatus as CustomerStatus]?.text}
                </Tag>
              }
              {base?.hasMallPermission && <Tag type="primary">已开通商城</Tag>}
            </div>
          </div>
        </div>
      </div>

      {/* 基本信息 */}
      <div className="bg-white mx-[28px] mt-[24px] rounded-[16px] p-[28px]">
        <div className="text-[32px] font-medium text-[#333] mb-[32px]">基本信息</div>

        <div className="space-y-[32px]">
          <div className="flex items-center gap-1">
            <span className="text-[#666]">ABN:</span>
            <span className="text-[#333]">{base?.abn || '-'}</span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">归属门店:</span>
            <span className="text-[#333]">{base?.storeName || '-'}</span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">业务员:</span>
            <span className="text-[#333]">{base?.salesmanName || '-'}</span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">客户标签:</span>
            <span className="text-[#333]">
              {tags?.map((tag) => tag.tagName).join(',') || '-'}
            </span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">通用邮箱:</span>
            <span className="text-[#333]">{base?.universalEmail || '-'}</span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">财务邮箱:</span>
            <span className="text-[#333]">{base?.financeEmail || '-'}</span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">发送财务邮件:</span>
            <span className="text-[#333]">
              {base?.sendFinanceEmailFlag ? '是' : '否'}
            </span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">运费类型:</span>
            <span className="text-[#333]">
              {DeliveryAmountTypeEnum[base?.deliveryAmountType as DeliveryAmountType]?.text || '-'}
            </span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">客户来源:</span>
            <span className="text-[#333]">{base?.sourceName || '-'}</span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">创建时间:</span>
            <span className="text-[#333]">{base?.createTime || '-'}</span>
          </div>

          <div className="flex items-center gap-1">
            <span className="text-[#666]">备注:</span>
            <span className="text-[#333] flex-1 ml-[20px]">
              {base?.remark || '-'}
            </span>
          </div>
        </div>
      </div>

      {/* 联系人信息 */}
      {!isEmpty(detail.contacts) && (
        <div className="bg-white mx-[28px] mt-[24px] rounded-[16px] p-[28px]">
          <div className="text-[32px] font-medium text-[#333] mb-[32px]">联系人信息</div>

          <div className="space-y-[32px]">
            {detail?.contacts
              ?.slice(0, contactsExpanded ? detail.contacts.length : 1)
              .map((contact, index) => (
                <div
                  key={index}
                  className="border-b border-[#F0F0F0] pb-[32px] last:border-b-0 last:pb-0"
                >
                  <div className="flex items-center gap-[16px] mb-[24px]">
                    <span className="text-[32px] font-medium text-[#333]">
                      {`${contact.firstName} ${contact.lastName}`}
                    </span>
                    {contact.isDefault == 1 && <Tag type="primary">默认联系人</Tag>}
                    {contact.positionList?.map((position, index) => (
                      <Tag key={index} type="info">
                        {position.positionName}
                      </Tag>
                    ))}
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="space-y-[20px]">
                      <div className="flex items-center gap-[20px]">
                        <Phone className="w-[32px] h-[32px]" color='#0000004D' />
                        <span className="text-[28px] text-[#666] flex-1">
                          {contact.phone || '-'}
                        </span>
                      </div>

                      <div className="flex items-center gap-[20px]">
                        <Mail className="w-[32px] h-[32px]" color='#0000004D' />
                        <span className="text-[28px] text-[#666]">{contact.email || '-'}</span>
                      </div>

                      <div className="flex items-center gap-[20px]">
                        <Notepad className="w-[32px] h-[32px]" color='#0000004D' />
                        <span className="text-[28px] text-[#666]">{contact.remark || '-'}</span>
                      </div>
                    </div>
                    {contact.phone && (
                      <div
                        className="w-[80px] h-[80px] rounded-full flex items-center justify-center"
                        onClick={() => makePhoneCall(contact.phone!)}
                      >
                        <Image width="32px" height="32px" src={iconCall}></Image>
                      </div>
                    )}
                  </div>
                </div>
              ))}

            {/* 显示全部联系人按钮 */}
            {detail.contacts && detail.contacts.length > 1 && (
              <div
                className="flex items-center justify-center gap-[8px] text-[28px] text-[#00000099] cursor-pointer"
                onClick={() => setContactsExpanded(!contactsExpanded)}
              >
                <span>{contactsExpanded ? '收起' : '显示全部联系人'}</span>
                {contactsExpanded ? (
                  <ArrowUp className={`w-[24px] h-[24px]`} color="#00000099" />
                ) : (
                  <ArrowDown color="#00000099" className={`w-[24px] h-[24px]`} />
                )}
              </div>
            )}
          </div>
        </div>
      )}
      {/* 地址信息 */}
      {!isEmpty(detail.addresses) && (
        <div className="bg-white mx-[28px] mt-[24px] rounded-[16px] p-[28px]">
          <div className="text-[32px] font-medium text-[#333] mb-[32px]">地址信息</div>

          <div className="space-y-[32px]">
            {detail?.addresses
              ?.slice(0, addressesExpanded ? detail.addresses.length : 1)
              .map((address, index) => (
                <div
                  key={index}
                  className="border-b border-[#F0F0F0] pb-[32px] last:border-b-0 last:pb-0"
                >
                  <div className="flex items-center gap-[16px] mb-[16px]">
                    <span className="text-[32px] font-medium text-[#333]">
                      {address.firstName} {address.lastName}
                    </span>
                    <span className="text-[28px] text-[#666]">{address.phone || '-'}</span>
                    {address.isDefault == 1 && <Tag type="primary">默认地址</Tag>}
                  </div>

                  {address.address && (
                    <div className="text-[28px] text-[#666] leading-[40px]">
                      {address.provinceName}
                      {address.cityName}
                      {address.prefectureName}
                      {address.address}
                    </div>
                  )}
                </div>
              ))}

            {/* 显示全部地址按钮 */}
            {detail.addresses && detail.addresses.length > 1 && (
              <div
                className="flex items-center justify-center gap-[8px] text-[28px] text-[#00000099] cursor-pointer"
                onClick={() => setAddressesExpanded(!addressesExpanded)}
              >
                <span>{addressesExpanded ? '收起' : '显示全部地址'}</span>
                {addressesExpanded ? (
                  <ArrowUp className={`w-[24px] h-[24px]`} color="#00000099" />
                ) : (
                  <ArrowDown color="#00000099" className={`w-[24px] h-[24px]`} />
                )}
              </div>
            )}
          </div>
        </div>
      )}
      {/* 结算信息 */}
      <div className="bg-white mx-[28px] mt-[24px] rounded-[16px] p-[28px]">
        <div
          className="flex items-center justify-between cursor-pointer mb-[32px]"
          onClick={() => setSettleExpanded(!settleExpanded)}
        >
          <span className="text-[32px] font-medium text-[#333]">结算信息</span>
          {settleExpanded ? (
            <ArrowUp className={`w-[24px] h-[24px]`} color="#00000099" />
          ) : (
            <ArrowDown className={`w-[24px] h-[24px]`} color="#00000099" />
          )}
        </div>

        {settleExpanded && (
          <div className="space-y-[32px] text-[28px]">
            <div className="flex justify-between items-center">
              <span>结算类型</span>
              <span>{detail.settle?.settleType || '-'}</span>
            </div>
            <Divider />
            <div >
              <div className="flex justify-between items-center">
                <span>挂账额度</span>
                <span>{detail.settle?.totalAmount || '-'}</span>
              </div>
              <div className="text-[24px] text-[#666] mt-1">已用${detail.settle?.usedAmount || '0'}，冻结${detail.settle?.freezeAmount || '0'}，可用${detail.settle?.availableAmount || '0'}</div>
            </div>
            <Divider />
            <div className="flex justify-between items-center">
              <span>支付种客户</span>
              <span>{detail.settle?.isMultiCurrency ? '是' : '否'}</span>
            </div>
            <Divider />
            <div className="flex justify-between items-center">
              <span>GST Excluded</span>
              <span>{detail.settle?.gstExcluded ? '是' : '否'}</span>
            </div>
          </div>
        )}
      </div>
      {/* 底部编辑按钮 */}
      <PermissionComponent permission="addCustomer">
        <div className="bg-white py-[32px] px-[28px] fixed left-0 right-0 bottom-0 border-t border-[#F0F0F0]">
          <div
            onClick={() => {
              RouterUtils.navigateTo({
                url: '/packages/customer/add/index',
                params: { cstId: detail.base?.id },
              });
            }}
            className="w-full bg-white text-[#333] text-[32px] border border-solid border-[#E0E0E0] rounded-[12px] py-[24px] flex justify-center items-center active:bg-[#F5F5F5]"
          >
            编辑客户
          </div>
          <SafeArea position="bottom"></SafeArea>
        </div>
      </PermissionComponent>

      {/* 底部安全区域占位 */}
      <div className="h-[200px]"></div>
    </div>
  );
};
