page,
html {
  background-color: #f5f5f5;

  // 确保页面滚动流畅
  .customer-detail-container {
    min-height: 100vh;
    padding-bottom: 200px; // 为底部按钮留出空间
  }

  // 卡片样式
  .detail-card {
    margin: 24px 28px 0;
    padding: 28px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  // 头像样式
  .customer-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    color: white;
    font-weight: 500;
    font-size: 32px;
    border-radius: 12px;

    // 根据客户名称生成不同颜色
    &.color-1 {
      background: #ff6b6b;
    }
    &.color-2 {
      background: #4ecdc4;
    }
    &.color-3 {
      background: #45b7d1;
    }
    &.color-4 {
      background: #96ceb4;
    }
    &.color-5 {
      background: #ffeaa7;
    }
    &.color-6 {
      background: #dda0dd;
    }
  }

  // 信息行样式
  .info-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;

    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
    }

    .label {
      color: #666;
      font-size: 28px;
    }

    .value {
      flex: 1;
      margin-left: 20px;
      color: #333;
      font-size: 28px;
      text-align: right;
    }
  }

  // 联系人项目样式
  .contact-item {
    padding: 24px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      padding-bottom: 0;
      border-bottom: none;
    }

    .contact-info {
      display: flex;
      gap: 20px;
      align-items: center;
      margin: 10px 0;

      .icon {
        width: 32px;
        height: 32px;
      }

      .text {
        flex: 1;
        color: #666;
        font-size: 28px;
      }
    }

    .call-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      background: #e7f0ff;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.2s;

      &:active {
        background: #d0e7ff;
        transform: scale(0.95);
      }
    }
  }

  // 底部按钮样式
  .bottom-button {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 32px 28px;
    background: white;
    border-top: 1px solid #f0f0f0;

    .edit-button {
      width: 100%;
      padding: 24px;
      color: #333;
      font-size: 32px;
      text-align: center;
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s;

      &:active {
        background: #f5f5f5;
        transform: scale(0.98);
      }
    }
  }
}
