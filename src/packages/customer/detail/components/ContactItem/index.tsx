import iconCall from '@/assets/icons/icon_call.png';
import { CustomerContactEntity } from '@/components/ChooseCstModal/types/CustomerContactEntity';
import { Mail, Notepad, Phone } from '@nutui/icons-react-taro';
import { Image } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
interface ContactItemProps {
  record: CustomerContactEntity;
  showCall?: boolean;
}
export default (props: ContactItemProps) => {
  const { record, showCall = false } = props;
  return (
    <div className="flex flex-col gap-[16px] text-secondary relative text-[28px]">
      <div className="flex gap-[20px] items-center">
        <Phone className="w-[32px] h-[32px]" color='#0000004D' />
        <span>{record.phone ?? '-'}</span>
      </div>
      <div className="flex gap-[20px] items-center">
        <Mail className="w-[32px] h-[32px]" color='#0000004D' />
        <span>{record.email ?? '-'}</span>
      </div>
      <div className="flex gap-[20px] items-center">
        <Notepad className="w-[32px] h-[32px]" color='#0000004D' />
        <span className="break-all flex-1 flex-shrink-0"> {record.remark ?? '-'}</span>
      </div>
      {record.phone && showCall && (
        <span
          className="absolute top-[34px] right-[32px]"
          onClick={() => {
            if (record.phone) {
              Taro.makePhoneCall({ phoneNumber: record.phone });
            }
          }}
        >
          <Image width="36px" height="36px" src={iconCall}></Image>
        </span>
      )}
    </div>
  );
};
