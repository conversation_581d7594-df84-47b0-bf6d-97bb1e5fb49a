import CustomNavBar from "@/components/CustomNavBar";
import CustomPicker from "@/components/CustomPicker";
import { ArrowRight } from "@nutui/icons-react-taro";
import { ConfigProvider, Input, Switch, TextArea } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro, { useLoad, useRouter } from '@tarojs/taro';
import { find, isEmpty, size, uniqueId } from 'lodash';
import { useEffect, useState } from 'react';
import { FormItemType } from '../../add';
import useCustomerAddStore from '../../add/store';
import { CustomerContactEntity, PositionEnum, PositionList } from '../../list/types/CustomerContactEntity';
import './index.scss';
export default () => {
  const { params } = useRouter();
  console.log(params.id);
  const store = useCustomerAddStore();
  const [contact, setContact] = useState<CustomerContactEntity>({ id: '' });
  const [visibleMap, setVisibleMap] = useState<Record<string, boolean>>({
    positionVisible: false,
  });
  useLoad(() => {
    let title = '';
    if (params.id) {
      title = '编辑联系人';
    } else {
      title = '新增联系人';
    }
    Taro.setNavigationBarTitle({ title });
  });
  useEffect(() => {
    if (params.id) {
      const contactItem = find(store.contacts, { id: params.id });
      if (contactItem) {
        setContact(contactItem);
      }
    }
  }, [params.id]);

  const FormItems: FormItemType<CustomerContactEntity>[] = [
    {
      key: 'firstName',
      label: 'First Name',
      name: 'firstName',
      value: contact.firstName,
      required: true,
    },
    {
      key: 'lastName',
      label: 'Last Name',
      name: 'lastName',
      value: contact.lastName,
      required: true,
    },
    { key: 'phone', label: '联系方式', name: 'phone', value: contact.phone },
    {
      key: 'position',
      label: '职务',
      name: 'position',
      value: contact.positions,
      type: 'select',
      valueEnum: PositionEnum,
      onClick: () => {
        setVisibleMap((pre) => ({ ...pre, positionVisible: true }));
      },
    },
    {
      key: 'email',
      label: '邮箱',
      name: 'email',
      value: contact.email,
    },
  ];
  return (
    <ConfigProvider
      theme={{
        nutuiInputPadding: '8px 0px',
        nutuiTextareaPadding: '0px 0px',
      }}
    >
      <CustomNavBar title={params.id ? "编辑联系人" : "新增联系人"} showBack={true} />
      <div className="p-[28px] flex flex-col gap-[24px] pb-[120px] text-main">
        <div id="customerAddForm" className="px-[28px] rounded-[16px] bg-white flex flex-col">
          {FormItems.map((t) => {
            const { key, name, label, placeholder = '请输入', value, required, type = 'input' } = t;
            return (
              <View key={key} className="formItem">
                <div className="flex justify-between items-center ">
                  <span className={`text-[32px] text-main  ${required && 'formItemRequired'}`}>
                    {label}
                  </span>
                  <span className="flex items-center text-[32px] gap-[24px]">
                    {
                      type == 'select' && <div className="flex items-center gap-[12px] py-[22px]" onClick={t.onClick}>
                        {
                          value ? <span>
                            {
                              value?.map?.((v) => {
                                return t.valueEnum[v]?.text
                              }).join(',')
                            }
                          </span> : <span className="text-[#757575] text-[32px]">请选择</span>
                        }

                        <ArrowRight color="#0000004D" height="26px" width="16px"></ArrowRight>
                      </div>
                    }
                    {type == 'input' && <Input
                      placeholder={placeholder}
                      placeholderClass="text-[#0000004D]"
                      align="right"
                      value={value}
                      onChange={(v) => {
                        setContact({ ...contact, [name]: v });
                      }}
                    ></Input>}
                  </span>
                </div>
              </View>
            );
          })}
        </div>
        <div className="p-[28px] rounded-[16px] bg-white flex justify-between items-center">
          <span className="text-[32px]">设为默认联系人</span>
          <Switch
            checked={contact.isDefault == 1}
            onChange={(value) => {
              setContact({ ...contact, isDefault: value ? 1 : 0 });
            }}
          ></Switch>
        </div>

        <div className="p-[28px] rounded-[16px] bg-white">
          <div className="flex justify-between items-center">
            <span className="text-[32px] mb-[32px]">备注</span>
            <span className="text-thirdary">{size(contact.remark)}/100</span>
          </div>
          <TextArea
            onChange={(value) => {
              setContact({ ...contact, remark: value });
            }}
            value={contact.remark}
            className="h-[100px]"
            maxLength={100}
            placeholder="请输入备注信息"
          />
        </div>
        <div className="py-[20px] relative z-10">
          {isEmpty(params.id) && (
            <span
              onClick={() => {
                if (isEmpty(contact.firstName) || isEmpty(contact.lastName)) {
                  Taro.showToast({ icon: 'none', title: '联系人为必填项' });
                  return;
                }
                let isDefault = contact.isDefault ?? 0;
                if (isEmpty(store.contacts)) {
                  isDefault = 1;
                }
                store.updateContact({ ...contact, id: uniqueId('contact_'), isDefault });
                Taro.navigateBack();
              }}
              className=" bg-[#F49C1F] text-[32px] text-white rounded-[8px] py-[16px] flex justify-center items-center"
            >
              新增
            </span>
          )}
          {params.id && (
            <>
              <span
                onClick={() => {
                  if (isEmpty(contact.firstName) || isEmpty(contact.lastName)) {
                    Taro.showToast({ icon: 'none', title: '联系人为必填项' });
                    return;
                  }
                  store.updateContact(contact);
                  Taro.navigateBack();
                }}
                className=" bg-[#F49C1F] text-[32px] text-white rounded-[8px] py-[16px] flex justify-center items-center"
              >
                保存
              </span>
              <span
                onClick={() => {
                  store.removeContact(params.id!);
                  Taro.navigateBack();
                }}
                className=" bg-white mt-[40px] text-[32px] text-main rounded-[8px] py-[16px] flex justify-center items-center border-[1px] border-solid border-[#00000073]"
              >
                删除联系人
              </span>
            </>
          )}
        </div>
      </div>
      <CustomPicker
        key="salesManSelect"
        items={PositionList}
        title="选择职务"
        visible={visibleMap.positionVisible}
        selected={contact?.positions}
        onClose={() => {
          setVisibleMap((pre) => ({ ...pre, positionVisible: false }));
        }}
        multiple
        onConfirm={(items) => {
          setContact({
            ...contact,
            positions: items,
            positionList: items.map((t) => ({ positionName: PositionEnum[t]?.text, positionCode: t })),
          });
          setVisibleMap((pre) => ({ ...pre, positionVisible: false }));
        }}
      />
    </ConfigProvider>
  );
};
