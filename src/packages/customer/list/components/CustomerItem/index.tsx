import iconCustDef from '@/assets/icons/icon_cust_def.png';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import { CustomerStatusEnum } from '@/packages/customer/detail/config/customerOptions';
import { Image, Tag } from '@nutui/nutui-react-taro';
import { defaultTo, size } from 'lodash';
import { useIntl } from 'react-intl';
import { CustomerItemRecordType } from '../../types/CustomerItemRecordType';
import { CustomerStatus } from '../../types/CustomerSaveEntity';
export interface CustomerItemProps {
  record: CustomerItemRecordType;
  source: string;
}
export default (props: CustomerItemProps) => {
  const intl = useIntl();
  const { record, source = 'detail' } = props;
  let tags = record.tags ?? [];
  const cstName = record.cstName;
  const cstSn = record.cstSn;
  const nickName = record.nickName;
  const defaultContact = record?.contacts?.[0];
  const usedAmount = record.usedAmount;
  const availableAmount = record.availableAmount;
  const creditAble = record.credit;
  const receivableAmount = defaultTo(record.receivableAmount, '-');
  const imageUrl = defaultTo(record?.images?.[0]?.url, iconCustDef);
  // const tagnames = tagList.map((t) => t.tagName).join('');

  return (
    <div className="flex gap-[20px]">
      <span className="flex-shrink-0">
        <Image radius={4} src={imageUrl} width="70px" height="70px"></Image>
      </span>
      <div className="flex-1 flex flex-col flex-grow">
        <div className="pb-[16px] flex flex-col">
          <span className="text-main text-[32px] font-medium">{cstName}</span>
          <div className="flex text-[26px] text-thirdary my-[12px]">
            {source == 'list' && (
              <ItemsWithDivider
                items={[
                  defaultTo((defaultContact?.firstName || defaultContact?.lastName) ? `${defaultContact?.firstName ?? ''} ${defaultContact?.lastName ?? ''}` : null, intl.formatMessage({ id: 'customer.list.item.noContactInfo' })),
                  defaultTo(defaultContact?.phone, intl.formatMessage({ id: 'customer.list.item.noContactInfo' })),
                ]}
              />
            )}
            {source == 'detail' && (
              <ItemsWithDivider items={[defaultTo(cstSn, ''), defaultTo(nickName, '')]} />
            )}
          </div>
          <div className="flex gap-[12px] flex-nowrap">
            <Tag type={CustomerStatusEnum[record?.cstStatus as CustomerStatus]?.status}>
              {record.cstStatusName}
            </Tag>
            {record.settleType && <Tag type='info'>
              {record.settleType}
            </Tag>}
            {tags &&
              tags.slice(0, 2).map((t) => {
                return <Tag type='info'>{t.tagName}</Tag>;
              })}
            {size(tags) > 2 && (
              <span className="num text-[20px] px-[12px] py-[6px] border rounded-[4px] bg-[#E7F0FFFF] text-[#176EFFFF] border-[#A2C5FFFF]">
                {`+${tags.length - 2}`}
              </span>
            )}
          </div>
        </div>
        {source == 'list' && (
          <div className="text-secondary text-[24px] flex flex-col gap-[8px]  pt-[16px] border-t-[1px] border-0 border-t-[#00000014] border-solid">
            <span>
              归属门店：{record.storeName || '-'};
              Suburb：{record.addresses?.[0]?.provinceName || '-'};
            </span>
            {creditAble && <><span>
              {intl.formatMessage({ id: 'customer.list.item.creditAmount' }, { total: defaultTo(record.totalAmount, 0), available: defaultTo(record.availableAmount, 0) })}
            </span>
              <span>
                客户应收： {defaultTo(record.receivableAmountCurrency, 0)} ;
                客户预收：{defaultTo(record.advanceAmountCurrency, 0)}
              </span>
            </>}
          </div>
        )}
      </div>
    </div>
  );
};
