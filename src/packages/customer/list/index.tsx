import '@/assets/lightPage.scss';
import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import CustomMultipleChoose, { ItemProps } from '@/components/CustomMultipleChoose';
import CustomNavBar from "@/components/CustomNavBar";
import GoodsSearchBar from '@/components/GoodsSearchBar';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import usePermissionStore from '@/pages/splash/permissionStore';
import RouterUtils from '@/utils/RouterUtils';
import { Menu, SafeArea } from '@nutui/nutui-react-taro';
import { useLoad } from '@tarojs/taro';
import { useAsyncEffect } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import { useIntl } from 'react-intl';
import { useShallow } from 'zustand/react/shallow';
import { getTagList } from '../add/services';
import useCustomerAddStore from '../add/store';
import CustomerItem from './components/CustomerItem';
import './index.scss';
import { getCstPaged } from './services';
export default () => {
  const { hasPermission } = usePermissionStore(
    useShallow((store) => ({
      hasPermission: store.hasPermission,
    })),
  );
  const { needReload, setState } = useCustomerAddStore(
    useShallow((store) => ({
      needReload: store.needReload,
      setState: store.setState,
    })),
  );
  const [tagList, setTagList] = useState<ItemProps[]>([]);
  useAsyncEffect(async () => {
    const data = await getTagList();
    if (data) {
      const tagList = data.map((item) => ({
        text: item.tagName ?? '',
        value: item.id ?? '',
      }));
      setTagList(tagList);
    }
  }, []);
  const orderStatusRef = useRef<any>();
  useEffect(() => {
    if (needReload) {
      setParams({ pageNo: 1, pageSize: 10 });
      setState('needReload', false);
    }
  }, [needReload]);
  useLoad(() => {
    console.log('Page loaded.');
  });
  const [params, setParams] = useState<any>({});
  const fetchData = (params) => {
    return getCstPaged({ ...params, pageSize: 10 }).then((result) => result?.data ?? []);
  };
  const orderStatusFilteredItems = [
    {
      label: '',
      keyStr: 'tagIds',
      multiple: true,
      item: tagList,
    },
  ];
  const intl = useIntl();
  return (
    <div className="h-screen flex flex-col">
      <CustomNavBar title={intl.formatMessage({ id: 'customer.list.title' })} showBack={true} />
      <GoodsSearchBar
        placeholder={intl.formatMessage({ id: 'customer.list.search.placeholder' })}
        inputValue={(query) => {
          setParams({ ...params, ...query });
        }}
        keyStr="cstWord"
        isShowScan={false}
        addUrl={hasPermission("addCustomer") ? "/packages/customer/add/index" : ''}
      />
      <MenuWrap
        menu={
          <Menu>
            <Menu.Item title={intl.formatMessage({ id: 'customer.list.menu.tag.filter' })} ref={orderStatusRef}>
              <CustomMultipleChoose
                key="tagIds"
                onClose={() => {
                  orderStatusRef.current?.toggle(false);
                }}
                selected={{
                  tagIds: params.tagIds ?? [],
                }}
                onConfirm={(e) => {
                  setParams({ ...params, ...e });
                }}
                items={orderStatusFilteredItems}
              />
            </Menu.Item>
          </Menu>
        }
      />
      <div className="px-[28px] flex-1 min-h-0 overflow-y-scroll">
        <FunPagination<CustomerEntity>
          params={params}
          fetchData={fetchData}
          renderItem={(record) => {
            return (
              <div
                className="p-[28px] rounded-[16px] bg-white flex flex-col gap-[20px]"
                onClick={() => {
                  RouterUtils.navigateTo({
                    url: '/packages/customer/detail/index',
                    params: {
                      cstId: record.cstId,
                    },
                  });
                }}
              >
                <CustomerItem source="list" record={record}></CustomerItem>
              </div>
            );
          }}
        />
      </div>
      <SafeArea position="bottom" />
    </div>
  );
};
