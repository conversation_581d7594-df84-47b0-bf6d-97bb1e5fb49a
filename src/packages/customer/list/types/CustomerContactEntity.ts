import { ItemProps } from "@/components/CustomPicker";
import { Contact } from "./CustomerSaveEntity";

export type CustomerContactEntity = Contact

export enum PositionType {
  BOSS = '1',
  PURCHASE = '2',
  FINANCE = '3',
  RECEPTION = '4',
  REPAIRMAN = '5',
}



export const PositionEnum = {
  [PositionType.BOSS]: {
    text: '老板',
  },
  [PositionType.PURCHASE]: {
    text: '采购',
  },
  [PositionType.FINANCE]: {
    text: '财务',
  },
  [PositionType.RECEPTION]: {
    text: '前台',
  },
  [PositionType.REPAIRMAN]: {
    text: '修理工',
  },
};

export const PositionList: ItemProps[] = [
  {
    title: '老板',
    value: PositionType.BOSS,
  },
  {
    title: '采购',
    value: PositionType.PURCHASE,
  },
  {
    title: '财务',
    value: PositionType.FINANCE,
  },
  {
    title: '前台',
    value: PositionType.RECEPTION,
  },
  {
    title: '修理工',
    value: PositionType.REPAIRMAN,
  }
];