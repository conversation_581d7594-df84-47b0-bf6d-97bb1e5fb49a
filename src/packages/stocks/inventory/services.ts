import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { post } from '@/utils/request';
import { PageResponseDataType } from '../../../../types/PageResponseDataType';
import { locationDelRequest } from './detail/types/location.del.request';
import { LocationListEntity } from './detail/types/location.list.entity';
import { locationScanRequest } from './detail/types/location.scan.request';
import { InventoryPostEntity } from './list/types/inventory.post.entity';
import { ListEntity } from './list/types/list.entity';
import { QueryPostDetailRequest } from './list/types/query.post.detail.request';
import { QueryPostListRequest } from './list/types/query.post.list.request';
import { TotalPostEntity } from './list/types/total.post.entity';
import { GoodsBrandEntity } from './types/GoodsBrand.entity';
import { GoodsCategoryEntity } from './types/GoodsCategory.entity';
import { SingleDetailEntity } from './types/SingleDetail.entity';
import { ChangeDetailEntity } from './types/change.detail.entity';
import { ChangeDetailRequest } from './types/change.detail.request';
import { itemLocationRemarkRquest } from './types/item.location.remark.request';
import { OccupyEntity } from './types/occupy.entity';

export type GoodsPropertyTableType = GoodsCategoryEntity & GoodsBrandEntity;

/**
 * 查询所有仓库列表
 * @param params
 * @returns
 */
export const warehouseList = (params: Partial<QueryPostDetailRequest>) => {
  return post<ListEntity>(`/ipmswarehouse/warehouseStoreRelation/queryList`, {
    data: params,
  });
};

/**
 * 商品属性-分页查询
 *
 * @param params
 * @returns
 */
export const queryGoodsPropertyPage = async (
  params: Partial<GoodsPropertyTableType> & PageRequestParamsType,
  requestType: string,
) => {
  const result = await post<PageResponseDataType<GoodsPropertyTableType>>(
    `/ipmsgoods/${requestType}/pageQuery`,
    { data: params },
  );
  if (result) {
    return {
      ...result,
      data: result.data.map((t) => ({ ...t })),
    };
  }
  return { data: [] };
};

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryInventoryPagePost = async (params: Partial<QueryPostListRequest>) => {
  return post<PageResponseDataType<InventoryPostEntity>>(`/ipmsstocks/inventory/queryByPage`, {
    data: params,
  });
};
/**
 * 查询库存和 金额
 * @param params
 * @returns
 */
export const queryTotalPost = async (params: Partial<QueryPostListRequest>) => {
  return post<TotalPostEntity>(`/ipmsstocks/inventory/queryTotal`, {
    data: params,
  });
};

/**
 * 库存详情
 * @param params
 * @returns
 */
export const querySingleDetailPost = (params: Partial<ChangeDetailRequest>) => {
  return post<SingleDetailEntity>(`/ipmsstocks/inventory/querySingleDetail`, {
    data: params,
  });
};

/**
 * 库存流水分页查询
 * @param params
 * @returns
 */
export const inventoryChangeDetailPost = (params: Partial<ChangeDetailRequest>) => {
  return post<PageResponseDataType<ChangeDetailEntity>>(
    `/ipmsstocks/inventoryChangeDetail/queryByPage`,
    {
      data: params,
    },
  );
};

/**
 * 根据仓库id查询库位
 * @param params
 * @returns
 */
export const queryLocationByWhId = async (params: { warehouseIdList: string[] }) => {
  return post<LocationListEntity>(`/ipmswarehouse/warehouseLocation/queryByWhId`, {
    data: params,
  });
};

/**
 * 编辑商品库位
 * @param params
 * @returns
 */
export const itemLocationRemarkPost = (params: Partial<itemLocationRemarkRquest>) => {
  return post<boolean>(`/ipmswarehouse/itemLocationRemark/saveOrUpdateList`, {
    data: params,
  });
};

export const itemLocationDelPost = (params: Partial<locationDelRequest>) => {
  return post<boolean>(`/ipmswarehouse/itemLocationRemark/delete`, {
    data: params,
  });
};
/**
 * 扫码添加库位
 * @param params
 * @returns
 */
export const itemLocationUpdateByCodePost = (params: Partial<locationScanRequest>) => {
  return post<boolean>(`/ipmswarehouse/itemLocationRemark/saveOrUpdateByCode`, {
    data: params,
  });
};

/**
 * 查询库存占用明细
 */
export const queryInventoryOccupy = (params: Partial<OccupyEntity> & PageRequestParamsType): Promise<PageResponseDataType<OccupyEntity>> => {
  return post(`/ipmsstocks/InventoryOccupyFacade/queryByPage`, {
    data: params,
  });
};