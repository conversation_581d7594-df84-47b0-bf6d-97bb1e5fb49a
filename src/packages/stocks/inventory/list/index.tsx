import '@/assets/lightPage.scss';
import notPic from '@/assets/not_pic.png';
import ChooseCategoryCard from '@/components/ChooseCategoryCard';
import CodeWithDivider from '@/components/CodeWithDivider';
import CustomMultipleChoose from '@/components/CustomMultipleChoose';
import GoodsSearchBar from '@/components/GoodsSearchBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import { Button, Menu, SafeArea } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import Taro, { navigateTo, useDidShow, useLoad, useRouter } from '@tarojs/taro';
import _, { isEmpty } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import {
  queryGoodsPropertyPage,
  queryInventoryPagePost,
  queryTotalPost,
  warehouseList,
} from '../services';
import { haveInvStatusOptions } from './types/HaveInvStatus';
import { haveItemCodeStatusOptions } from './types/HaveItemCodeStatus';
import { invLimitStatusOptions } from './types/InvLimitStatus';
import { InventoryPostEntity } from './types/inventory.post.entity';
import { QueryPostListRequest } from './types/query.post.list.request';
import { TotalPostEntity } from './types/total.post.entity';

export default function Index() {
  const router = useRouter();
  const { initInvLimitStatusList, initWarehouseIdList } = router.params;
  const homeNavigateParamWarehouseIdList = initWarehouseIdList?.split(',') ?? undefined;
  const homeNavigateParamInvLimitStatusList = initInvLimitStatusList
    ? { invLimitStatusList: initInvLimitStatusList?.split(',')?.map((item) => Number(item)) }
    : undefined;
  const [wareHouseOptions, setWareHouseOptions] = useState<any>([]);
  const [filteredItems, setFilteredItems] = useState<any>([]);
  const [brandItems, setBrandItems] = useState<any>([]);
  const [params, setParams] = useState<any>({
    ...homeNavigateParamInvLimitStatusList,
    warehouseIdList: homeNavigateParamWarehouseIdList,
  });
  const [wareHouseTitle, setWareHouseTitle] = useState<string>('仓库');
  const [invLimitTitle, setInvLimitTitle] = useState<string>('品牌');
  const [haveInvStatusTitle, setHaveInvStatusTitle] = useState<string>('分类');
  const [haveInvStatus, setHaveInvStatus] = useState<string | undefined>(undefined);
  const [locationItem, setLocationItem] = useState<any>(homeNavigateParamInvLimitStatusList);
  const [totalData, setTotalData] = useState<TotalPostEntity>({});
  const [onLoadFlag, setOnLoadFlag] = useState<boolean>(false);
  const [warehouseIdList, setWarehouseIdList] = useState<string[] | undefined>(
    homeNavigateParamWarehouseIdList,
  );
  const [brandIdList, setBrandIdList] = useState<any | undefined>(undefined);
  const categoryMenuRef = useRef<any>();
  const isFirstRender = useRef(true);

  useLoad(async () => {
    loadItem();
  });

  useDidShow(() => {
    if (onLoadFlag) {
      setParams((prevData) => ({ ...prevData, time: _.now() }));
    }
  });

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    setParams((prevData) => ({
      ...prevData,
      haveInvStatus,
      ...brandIdList,
      warehouseIdList,
      ...locationItem,
    }));
  }, [haveInvStatus, brandIdList, warehouseIdList, locationItem]);

  const loadItem = async () => {
    setFilteredItems([
      {
        label: '库存预警',
        keyStr: 'invLimitStatusList',
        multiple: true,
        item: invLimitStatusOptions,
      },
      { label: '库存情况', keyStr: 'haveInv', multiple: false, item: haveInvStatusOptions },
      {
        label: '库存状态',
        keyStr: 'haveItemCode',
        multiple: false,
        item: haveItemCodeStatusOptions,
      },
    ]);
    const { warehouseSimpleRoList } = await warehouseList({});
    if (!isEmpty(warehouseSimpleRoList)) {
      setWareHouseOptions(
        warehouseSimpleRoList?.map((s) => ({ text: s.warehouseName, value: s.id })),
      );
    }
    const { data: brandData } = await queryGoodsPropertyPage(
      { pageNo: 1, pageSize: 1000, brandStatus: '1' },
      'brand',
    );
    if (brandData) {
      setBrandItems([
        {
          keyStr: 'brandIdList',
          multiple: true,
          label: '',
          item: brandData.map((s) => ({ text: s.brandName, value: s.brandId })),
        },
      ]);
    }
  };

  /**
   * 查询 方法
   * @param params
   * @returns
   */
  const loadData = async (param: QueryPostListRequest) => {
    const dataTotal = await queryTotalPost(param);
    setTotalData(dataTotal);
  };

  const fetchData = (param?: QueryPostListRequest) => {
    loadData({ ...param }); //统计方法
    return queryInventoryPagePost({ pageSize: 10, ...param, showItemExtra: true }).then(
      (result) => result?.data ?? [],
    );
  };

  //搜索条件
  const setInputValue = (param) => {
    setParams((prevData) => ({
      ...prevData,
      ...param,
    }));
    loadData({ ...params, ...param });
  };

  const renderItem = (record: InventoryPostEntity, index: number) => (
    <div
      className="my-[24px] bg-white mx-[28px] py-[24px] px-[28px] rounded-[16px]"
      onClick={() => {
        navigateTo({
          url:
            '/packages/stocks/inventory/detail/index?warehouseId=' +
            record.warehouseId +
            '&itemId=' +
            record.itemId,
        });
        setOnLoadFlag(true);
      }}
    >
      <div className="flex justify-start">
        <div>
          <Image
            src={record.images?.[0] ?? notPic}
            className="rounded bg-[#f5f5f5]"
            style={{ width: '44px', height: '44px' }}
          />
        </div>
        <div className="pl-[20px] flex-1">
          <div className="flex justify-between items-center">
            <span className="text-[32px] font-medium text-[#111111] leading-[1.5]">{record.itemName}</span>
            <PermissionComponent permission={'inventoryPrintGoodTag'}>
              <Button
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  Taro.navigateTo({
                    url: `/packages/print/goods/index`,
                    success(res) {
                      res.eventChannel.emit('goods', {
                        goods: [
                          {
                            itemSn: record.itemSn,
                            number: record.inventoryNum,
                          },
                        ],
                      });
                    },
                  });
                }}
              >
                打印
              </Button>
            </PermissionComponent>
          </div>
          <div className="pb-[12px] text-[24px] text-[#999999] flex">
            <ItemsWithDivider
              items={[record?.itemSn, record?.brandName, record?.categoryName, record?.unitName]}
            />
          </div>
        </div>
      </div>
      <div className="flex justify-start pt-[16px] text-[#666666] text-[24px]">
        <div className="flex-1 flex-col">
          <div>库存总数 {record.inventoryNum}</div>
          <div className="pt-[16px]">库存下限 {record.lowerLimit}</div>
          <div className="pt-[16px]">仓库 {record.warehouseName}</div>
        </div>
        <div className="flex-1 flex-col">
          <div>可用数量 {record.avaNum}</div>
          <div className="pt-[16px] ">库存上限 {record.upperLimit}</div>
          <div className="pt-[16px] flex items-center">
            <span className="pr-[16px]">库位</span>
            <CodeWithDivider key={record.id} title="库位" items={record.code?.split(',')} />
          </div>
        </div>
      </div>
    </div>
  );
  const itemRef = useRef(null);
  const itemOneRef = useRef(null);

  return (
    <div className="h-screen flex flex-col">
      <div className="mt-[16px] mb-[24px]">
        <GoodsSearchBar inputValue={setInputValue} keyStr="keyword" />
      </div>
      <div className="custom-menu">
        <MenuWrap
          menu={
            <Menu
              onClose={(i: number) => console.log('onClose', i)}
              onOpen={(i: number) => console.log('onOpen', i)}
            >
              <Menu.Item
                title={'仓库'}
                defaultValue={warehouseIdList?.[0] ?? ''}
                options={[{ text: '全部', value: '' }, ...wareHouseOptions]}
                onChange={(e) => {
                  if (e.value === '') {
                    setWarehouseIdList([]);
                  } else {
                    setWarehouseIdList([e.value]);
                  }
                }}
              ></Menu.Item>
              <Menu.Item
                title={invLimitTitle}
                onChange={(e) => setInvLimitTitle(e?.text)}
                ref={itemOneRef}
              >
                <CustomMultipleChoose
                  key={'barnItems'}
                  onClose={() => {
                    (itemOneRef.current as any)?.toggle(false);
                  }}
                  onConfirm={(e) => {
                    setBrandIdList(e);
                  }}
                  items={brandItems}
                />
              </Menu.Item>
              <Menu.Item title={haveInvStatusTitle} ref={categoryMenuRef}>
                <ChooseCategoryCard
                  values={params.categoryIdList ?? []}
                  onChange={(v) => {
                    setParams({ ...params, categoryIdList: v });
                    categoryMenuRef.current?.toggle(false);
                  }}
                />
              </Menu.Item>
              <Menu.Item title={'库存库位'} ref={itemRef}>
                <CustomMultipleChoose
                  key={'locationKey'}
                  onClose={() => {
                    (itemRef.current as any)?.toggle(false);
                  }}
                  selected={locationItem}
                  onConfirm={(e) => {
                    setLocationItem(e);
                  }}
                  items={filteredItems}
                />
              </Menu.Item>
            </Menu>
          }
        />
      </div>
      <div className="flex justify-start bg-white mx-[28px] p-[28px] mt-[24px] rounded-[16px]">
        <div className="flex-1">
          <div className="text-[#666666] text-[28px] ">库存总数</div>
          <div className="text-primary text-[44px] pt-2">{totalData?.totalInventory}</div>
        </div>
        <div className="flex-1">
          <div className="text-[#666666] text-[28px] ">库存金额</div>
          <div className="text-primary text-[44px] pt-2">{totalData?.totalCostPrice}</div>
        </div>
      </div>
      <div className="flex-1 min-h-0">
        <FunPagination fetchData={fetchData} params={params} renderItem={renderItem} />
      </div>
      <SafeArea position="bottom" />
    </div>
  );
}
