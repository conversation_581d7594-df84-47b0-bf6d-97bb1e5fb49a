import notPic from '@/assets/not_pic.png';
import Card from '@/components/Card';
import CustomNavBar from '@/components/CustomNavBar';
import CustomPicker from '@/components/CustomPicker';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import FunPagination from '@/components/Pagination/FunPagination';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import { Del } from '@nutui/icons-react-taro';
import { Divider, SafeArea, Tabs } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import { showToast, useLoad } from '@tarojs/taro';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import {
  inventoryChangeDetailPost,
  itemLocationDelPost,
  itemLocationRemarkPost,
  itemLocationUpdateByCodePost,
  queryInventoryOccupy,
  queryLocationByWhId,
  querySingleDetailPost,
} from '../services';
import { SingleDetailEntity } from '../types/SingleDetail.entity';
import { ChangeDetailEntity } from '../types/change.detail.entity';
import { ChangeDetailRequest } from '../types/change.detail.request';
import { OccupyEntity } from '../types/occupy.entity';
import './index.scss';

export default function Index() {
  const [recordData, setRecordData] = useState<SingleDetailEntity>({});
  const [paramData, setParamData] = useState({});
  const [locationData, setLocationData] = useState<{ title: string; value: string }[]>([]);
  const [value, setValue] = useState(0);

  useLoad(({ warehouseId, itemId }) => {
    loadData({ itemId, warehouseId });
    setParamData({ itemId, warehouseId });
    loadLocationByWhId({ warehouseIdList: [warehouseId] });
  });

  const loadData = async (params: ChangeDetailRequest) => {
    const data = await querySingleDetailPost({ ...params, showItemExtra: true });
    setRecordData(data);
  };

  const loadLocationByWhId = async (params) => {
    const data = await queryLocationByWhId(params);
    setLocationData(data?.warehouseLocationRoList?.map((s) => ({ title: s.code!, value: s.id! })));
  };

  const [isPickerVisible, setIsPickerVisible] = useState<boolean>(false);

  const handleOpenPicker = () => {
    setIsPickerVisible(true);
  };

  const handleClosePicker = () => {
    setIsPickerVisible(false);
  };
  //编辑库位
  const handleConfirmSelection = async (locationIdList: string[]) => {
    console.log('Selected items:', locationIdList);
    const data = await itemLocationRemarkPost({
      itemLocationRemarkCmdList: [{ ...paramData, locationIdList }],
    });
    if (data) {
      //刷新
      loadData(paramData);
    }
  };

  /**
   * 删除库位
   * @param id
   */
  const handleDelLocation = async (id) => {
    const data = await itemLocationDelPost({ id });
    if (data) {
      loadData(paramData);
    }
  };

  const fetchData = (param?: ChangeDetailRequest) => {
    if (isEmpty(param?.itemId)) {
      return [];
    }
    return inventoryChangeDetailPost({ pageSize: 10, ...param }).then(
      (result) => result?.data ?? [],
    );
  };

  const fetchOccupyData = (param?: OccupyEntity) => { return queryInventoryOccupy({ ...param, pageSize: 10 }).then((result) => result?.data ?? []); }

  const handleScanAdd = async (code) => {
    if (code) {
      const data = await itemLocationUpdateByCodePost({ ...paramData, code });
      if (data) {
        loadData(paramData);
      }
    } else {
      showToast({
        title: '扫码失败，请重试！',
        icon: 'none',
        duration: 2000,
      });
      return;
    }
  };

  const renderItem = (record: ChangeDetailEntity, index: number) => (
    <div>
      <div className={index == 0 ? 'pt-[8px] pb-[24px]' : 'py-[24px]'}>
        <div className="flex justify-between text-[32px]">
          <div className="text-[#111111]">{record.billTypeDesc}</div>
          <div className={record?.changeNum! >= 0 ? 'text-primary flex' : 'text-[#33CC47] flex'}>
            <span>
              {record.changeNum! > 0 && '+'}
              {record.changeNum! < 0 && ''}
            </span>
            {record.changeNum}
          </div>
        </div>
        <div className="flex justify-between text-[28px] text-[#999999] pt-[12px]">
          <div className="break-all">{record.origBillNo}</div>
          <div>{record.changeTime}</div>
        </div>
      </div>
      <Divider />
    </div>
  );

  const renderOccupyItem = (record: OccupyEntity, index: number) => (
    <div>
      <div className={index == 0 ? 'pt-[8px] pb-[24px]' : 'py-[24px]'}>
        <div className="flex justify-between text-[32px]">
          <div className="text-[#111111]">{record.billTypeDesc}</div>
          <div className="text-primary flex">
            <span>
              x{record.occupyNum}
            </span>
          </div>
        </div>
        <div className="flex justify-between text-[28px] text-[#999999] pt-[12px]">
          <div className="break-all">{record.origBillNo}</div>
          <div>{record.updateTime}</div>
        </div>
      </div>
      <Divider />
    </div>
  );

  return (
    <div className="h-screen flex flex-col">
      <CustomNavBar title="库存详情" />
      <div className="flex-1 min-h-0 flex flex-col">
        <div className="flex justify-start pt-[32px] px-[28px]">
          <div>
            <Image
              src={recordData?.images?.[0] ?? notPic}
              className="rounded bg-[#f5f5f5]"
              style={{ width: '44px', height: '44px' }}
            />
          </div>
          <div className="pl-[20px]">
            <div className="text-[32px] font-medium text-[#111111] leading-[1.5]">{recordData?.itemName}</div>
            <div className="py-[12px] text-[24px] text-[#999999] flex">
              <ItemsWithDivider
                items={[
                  recordData?.itemSn,
                  recordData?.brandName,
                  recordData?.categoryName,
                  recordData?.unitName,
                ]}
              />
            </div>
          </div>
        </div>
        <Card title={recordData?.warehouseName}>
          <div className="flex justify-between ">
            <div className="flex flex-col gap-[8px] w-[210px]">
              <div className="text-[24px] text-[#999999]">库存总数</div>
              <div className="text-[32px] text-[#111111]">{recordData?.inventoryNum}</div>
            </div>
            <div className="flex flex-col gap-[8px] w-[210px]">
              <div className="text-[24px] text-[#999999]">占用数量</div>
              <div className="text-[32px] text-[#111111] ">{recordData?.lockedNum}</div>
            </div>
            <div className="flex flex-col gap-[8px] w-[210px]">
              <div className="text-[24px] text-[#999999]">可用数量</div>
              <div className="text-[32px] text-[#111111]">{recordData?.avaNum}</div>
            </div>
          </div>
          <div className="flex justify-between pt-[24px]">
            <div className="flex flex-col gap-[8px] w-[210px]">
              <div className="text-[24px] text-[#999999]">成本单价</div>
              <div className="text-[32px] text-[#111111]">{recordData?.costPrice}</div>
            </div>
            <div className="flex flex-col gap-[8px] w-[210px]">
              <div className="text-[24px] text-[#999999]">库存上限</div>
              <div className="text-[32px] text-[#111111]">{recordData?.upperLimit}</div>
            </div>
            <div className="flex flex-col gap-[8px] w-[210px]">
              <div className="text-[24px] text-[#999999]">库存下限</div>
              <div className="text-[32px] text-[#111111]">{recordData?.lowerLimit}</div>
            </div>
          </div>
        </Card>
        <div className="mx-[28px] bg-white rounded-[16px] flex-1 flex flex-col min-h-0 overflow-y-auto">
          <Tabs
            align="left"
            activeType="smile"
            autoHeight
            className="min-h-0 inventoryDetail"
            tabStyle={{
              backgroundColor: 'white',
              '--nutui-tabs-titles-item-min-width': '30px',
              borderRadius: '8px',
            }}
            style={{ '--nutui-tabs-tabpane-padding': '8px 28px' }}
            value={value}
            onChange={(v) => {
              setValue(v);
            }}
          >
            <Tabs.TabPane title="库位" >
              <div>
                {recordData?.itemInventoryLocationRoList?.map((item) => (
                  <>
                    <div className="flex justify-between py-[32px]">
                      <div className="text-[32px] text-black/90">{item.code}</div>
                      <PermissionComponent permission={'setLocation'}>
                        <div onClick={() => handleDelLocation(item.id)}>
                          <Del color="#0000004D" width="16px" height="16px" />
                        </div>
                      </PermissionComponent>
                    </div>
                    <Divider />
                  </>
                ))}
                {/* <PermissionComponent permission={'setLocation'}>
                  <div className="flex justify-between pt-[24px] text-[32px] gap-[22px]">
                    <div
                      className="py-[14px] flex-1 text-center rounded-[8px] border border-solid border-[#999999] "
                      onClick={() => {
                        //扫码
                        scanCode().then(result => {
                          if (result) {
                            handleScanAdd(result);
                          }
                        })
                      }}
                    >
                      扫码添加
                    </div>
                    <div
                      className=" py-[14px] flex-1 text-center rounded-[8px] border border-solid border-[#999999] "
                      onClick={handleOpenPicker}
                    >
                      手动添加
                    </div>
                  </div>
                </PermissionComponent> */}
              </div>
            </Tabs.TabPane>
            <Tabs.TabPane title="占用单据">
              <div className="flex-1 min-h-0 overflow-y-scroll h-[100%]">
                <FunPagination fetchData={fetchOccupyData} params={{ ...paramData, stateList: [1] }} renderItem={renderOccupyItem} />
              </div>
            </Tabs.TabPane>
            <Tabs.TabPane title="库存流水">
              <div className="flex flex-1 min-h-0 overflow-y-scroll h-[100%]">
                <FunPagination fetchData={fetchData} params={paramData} renderItem={renderItem} />
              </div>
            </Tabs.TabPane>
          </Tabs>
        </div>
        <SafeArea position="bottom" />
      </div>
      <CustomPicker
        visible={isPickerVisible}
        onClose={handleClosePicker}
        title="添加库位"
        placeholder="库位名称"
        items={locationData}
        selected={recordData?.itemInventoryLocationRoList?.map((s) => s.locationId!)}
        onConfirm={handleConfirmSelection}
      />
    </div>
  );
}
