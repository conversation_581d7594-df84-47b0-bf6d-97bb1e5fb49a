export interface OccupyEntity {
  /**
   * 单据类型
   */
  billType?: number;
  /**
   * 单据类型
   */
  billTypeDesc?: string;
  /**
   * 业务单号
   */
  bizBillNo?: string;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 商品id
   */
  itemId?: number;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 占用数量
   */
  occupyNum?: number;
  /**
   * 占用时间
   */
  occupyTime?: string;
  /**
   * 原单号
   */
  origBillNo?: string;
  /**
   * None
   */
  realOccupyNum?: number;
  /**
   * 释放数量
   */
  releaseNum?: number;
  /**
   * 流水号
   */
  serialNumber?: string;
  /**
   * 占用状态：1：占用中2：完成3：释放
   */
  state?: number;
  /**
   * 三方明细行id
   */
  thirdDetailId?: number;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 仓库id
   */
  warehouseId?: number;
}
