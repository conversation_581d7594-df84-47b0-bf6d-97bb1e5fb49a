export interface SingleDetailEntity {
  /**
   * 可用库存
   */
  avaNum?: number;
  /**
   * 品牌Id
   */
  brandId?: string;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 供应商编码
   */
  brandPartNo?: string;
  /**
   * 类目Id
   */
  categoryId?: string;
  /**
   * 类目名称
   */
  categoryName?: string;
  /**
   * 成本价
   */
  costPrice?: number;
  /**
   * 库存主键id
   */
  id?: string;
  /**
   * 图片列表
   */
  images?: string[];
  /**
   * 账面库存
   */
  inventoryNum?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 库位
   */
  itemInventoryLocationRoList?: ItemInventoryLocationRoList[];
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 冻结库存
   */
  lockedNum?: number;
  /**
   * 安全库存下限
   */
  lowerLimit?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * oe码
   */
  oeNo?: string;
  /**
   * ETC号
   */
  thirdNo?: string;
  /**
   * 总成本价=成本价数量
   */
  totalCostPrice?: number;
  /**
   * 在途库存
   */
  transitNum?: number;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * 安全库存上限
   */
  upperLimit?: number;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}

export interface ItemInventoryLocationRoList {
  /**
   * 货位code
   */
  code?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 库位id
   */
  locationId?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 仓库id
   */
  warehouseId?: string;
}
