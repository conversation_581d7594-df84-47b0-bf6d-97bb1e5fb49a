import CustomMultipleChoose from '@/components/CustomMultipleChoose';
import CustomNavBar from '@/components/CustomNavBar';
import CustomSearchBar from '@/components/CustomSearchBar';
import CustomSingleChoose from '@/components/CustomSingleChoose';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import { TimeFormat } from '@/components/TimeFormat';
import { Divider, Menu, SafeArea, Tag } from '@nutui/nutui-react-taro';
import Taro, { useLoad } from '@tarojs/taro';
import { isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import { warehouseList } from '../../inventory/services';
import { queryDeliveryPage } from '../services';
import { DeliveryEntity } from '../types/delivery.entity';
import { DeliveryState, DeliveryStateMap, DeliveryType } from '../types/delivery.enums';

export default function DeliveryList() {
  const [params, setParams] = useState<Partial<DeliveryEntity> & {
    bizBillNo?: string;
    deliveryTargetName?: string;
    states?: DeliveryState[];
    billType?: DeliveryType;
    warehouseIds?: string[];
    sort?: ''
  }>({});

  const statusRef = useRef<any>();
  const orderRef = useRef<any>();
  const typeRef = useRef<any>();
  const warehouseRef = useRef<any>();
  const [wareHouseOptions, setWareHouseOptions] = useState<any>([]);

  // 搜索条件配置
  const searchItems = [
    { key: 'origBillNo', name: '业务单号', scanShow: true, placeholder: '请输入业务单号' },
    { key: 'deliveryTargetName', name: '配送对象', scanShow: false, placeholder: '请输入配送对象名称' },
  ];

  // 筛选条件配置
  const filterItems = [
    {
      keyStr: 'sort',
      label: '排序',
      item: [
        { text: '创建时间升序', value: 'createTime-ASC' },
        { text: '创建时间降序', value: 'createTime-DESC' },
        { text: '期望到达时间降序', value: 'expectedArrTime-ASC' },
      ],
    },
    {
      keyStr: 'stateList',
      multiple: true,
      label: '配送状态',
      item: [
        { text: '待领取', value: DeliveryState.PENDING },
        { text: '已领取', value: DeliveryState.PICKED_UP },
        { text: '配送中', value: DeliveryState.IN_DELIVERY },
        { text: '配送完成', value: DeliveryState.COMPLETED },
        { text: '已取消', value: DeliveryState.CANCELLED },
      ],
    },
    {
      keyStr: 'billType',
      multiple: false,
      label: '任务类型',
      item: [
        { text: '送货', value: DeliveryType.DELIVERY },
        { text: '取货', value: DeliveryType.PICKUP },
      ],
    },
    {
      keyStr: 'warehosueIdList',
      multiple: true,
      label: '仓库',
      item: wareHouseOptions
    },
  ];

  useLoad(async () => {
    const { warehouseSimpleRoList } = await warehouseList({});
    if (!isEmpty(warehouseSimpleRoList)) {
      setWareHouseOptions(
        warehouseSimpleRoList?.map((s) => ({ text: s.warehouseName, value: s.id })),
      );
    }
  });

  const setInputValue = (param: any) => {
    setParams((prevData) => ({ ...prevData, ...param }));
  };

  const fetchData = (query: any) => {
    return queryDeliveryPage({ ...query, ...params, pageSize: 10 }).then(
      (result) => result?.data ?? [],
    );
  };



  // 跳转到详情页
  const navigateToDetail = (id?: string) => {
    if (id) {
      Taro.navigateTo({
        url: `/packages/stocks/delivery/detail/index?id=${id}`
      });
    }
  };

  // 渲染配送任务项
  const renderDeliveryItem = (record: DeliveryEntity) => {
    return (
      <div
        className="bg-white rounded-[16px] mx-[28px] mb-[24px] cursor-pointer"
        onClick={() => navigateToDetail(record.id)}
      >
        {/* 头部信息 */}
        <div className="flex justify-between items-start p-[28px]">
          <div className="flex-1">
            <div className="text-[32px] font-medium mb-[8px]">
              {record.deliveryTargetName}
            </div>
            <div className="text-[24px] text-[#00000073]">
              {record.bizBillNo}
            </div>
          </div>
          <div className="flex gap-[8px] flex-wrap">
            {record.isUrgent === 1 && (
              <Tag type="danger" color='#D1382BFF'>紧急</Tag>
            )}
            <Tag type="info">
              {record.billTypeDesc}
            </Tag>
            <Tag type={DeliveryStateMap[record.state!]?.status} color={
              DeliveryStateMap[record.state!]?.color
            }>
              {record.stateDesc}
            </Tag>
          </div>
        </div>
        <Divider />

        {/* 详细信息 */}
        <div className="space-y-[18px] p-[28px] text-[#00000099]">
          <div className="flex text-[28px]">
            <span className="w-[140px]">业务单号</span>
            <span className="flex-1">{record.origBillNo}</span>
            <span className="text-[#000000E6]">x {record.totalAmount}</span>
          </div>

          {record?.orderComplete && <div className="flex text-[28px]">
            <span className="w-[140px]">订单金额</span>
            <span className="flex-1">
              <span>
                {record?.orderComplete?.orderPrice?.shouldTotalOrderAmountYuan ?? '-'}
              </span>
              <span className='ml-1'>
                {record?.orderComplete?.orderPayList?.[0]?.payTypeName ?? '-'}
              </span>
            </span>
          </div>}

          <div className="flex text-[28px]">
            <span className="w-[140px]">仓库</span>
            <span className="flex-1">{record.warehouseName}</span>
          </div>

          <div className="flex text-[28px]">
            <span className="w-[140px]">地址</span>
            <span className="flex-1">{record.deliveryAddress}</span>
          </div>

          <div className="flex text-[28px]">
            <span className="w-[140px]">创建时间</span>
            <span className="flex-1">
              {record.beginTime ? <TimeFormat time={record.beginTime} showTime /> : '-'}
            </span>
          </div>

          <div className="flex text-[28px]">
            <span className="w-[140px]">期望到达</span>
            <span className="flex-1">
              {record.expectedArrTime ? <TimeFormat time={record.expectedArrTime} showTime /> : '-'}
            </span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-screen">
      <CustomNavBar title="配送任务" />

      {/* 搜索栏 */}
      <div className="px-[28px] py-[20px]">
        <CustomSearchBar
          itemList={searchItems}
          defaultItem={searchItems[0]}
          inputValue={setInputValue}
        />
      </div>

      {/* 筛选菜单 */}
      <MenuWrap
        menu={
          <Menu>
            <Menu.Item title="排序" ref={orderRef}>
              <CustomSingleChoose
                items={[filterItems[0]]}
                selected={params}
                onConfirm={(selectedMap) => {
                  const { sort = '' } = selectedMap;
                  const [sortField, sortOrder] = sort.split('-');
                  setParams((prev) => ({ ...prev, sortField, sortOrder, sort }));
                  orderRef.current?.toggle(false);
                }}
                onClose={() => orderRef.current?.toggle(false)}
              />
            </Menu.Item>
            <Menu.Item title="配送状态" ref={statusRef}>
              <CustomMultipleChoose
                items={[filterItems[1]]}
                selected={params}
                onConfirm={(selectedMap) => {
                  setParams((prev) => ({ ...prev, ...selectedMap }));
                  statusRef.current?.toggle(false);
                }}
                onClose={() => statusRef.current?.toggle(false)}
              />
            </Menu.Item>
            <Menu.Item title="任务类型" ref={typeRef}>
              <CustomSingleChoose
                items={[filterItems[2]]}
                selected={params}
                onConfirm={(selectedMap) => {
                  setParams((prev) => ({ ...prev, ...selectedMap }));
                  typeRef.current?.toggle(false);
                }}
                onClose={() => typeRef.current?.toggle(false)}
              />
            </Menu.Item>
            <Menu.Item title="仓库" ref={warehouseRef}>
              <CustomMultipleChoose
                items={[filterItems[3]]}
                selected={params}
                onConfirm={(selectedMap) => {
                  setParams((prev) => ({ ...prev, ...selectedMap }));
                  warehouseRef.current?.toggle(false);
                }}
                onClose={() => warehouseRef.current?.toggle(false)}
              />
            </Menu.Item>
          </Menu>
        }
      />

      {/* 列表内容 */}
      <div className="flex-1 min-h-0 overflow-scroll mt-[20px]">
        <FunPagination
          fetchData={fetchData}
          params={params}
          renderItem={renderDeliveryItem}
        />
      </div>

      <SafeArea position="bottom" />
    </div>
  );
}