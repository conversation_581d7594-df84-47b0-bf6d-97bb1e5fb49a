
import { IntlShape } from 'react-intl';

export enum StatusEnum {
  NotCompleted = 1,
  Completed = 2,
  Cancelled = 3,
}

export const statusEnumMap = {
  [StatusEnum.NotCompleted]: {
    text: '未完成',
    color: '#D1382B',
    backgoundColor: '#FFEEEE',
    type: 'danger' as const,
  },
  [StatusEnum.Completed]: {
    text: '已完成',
    color: '#01A87C',
    backgoundColor: '#CCEEE5',
    type: 'success' as const,
  },
  [StatusEnum.Cancelled]: {
    text: '已取消',
    color: '#999999',
    backgoundColor: '#F5F5F5',
    type: 'default' as const,
  },
};

export const getStatusEnumMap = (intl: IntlShape) => ({
  [StatusEnum.NotCompleted]: {
    text: intl.formatMessage({ id: 'task.status.notCompleted' }),
    color: '#D1382B',
    backgoundColor: '#FFEEEE',
    type: 'danger' as const,
  },
  [StatusEnum.Completed]: {
    text: intl.formatMessage({ id: 'common.status.completed' }),
    color: '#01A87C',
    backgoundColor: '#CCEEE5',
    type: 'success' as const,
  },
  [StatusEnum.Cancelled]: {
    text: intl.formatMessage({ id: 'common.status.cancelled' }),
    color: '#999999',
    backgoundColor: '#F5F5F5',
    type: 'default' as const,
  },
});