import CustomMultipleChoose from "@/components/CustomMultipleChoose";
import CustomNavBar from "@/components/CustomNavBar";
import FilterAccountPicker from "@/components/FilterAccountPicker";
import MenuWrap from "@/components/MenuWrap";
import FunPagination from "@/components/Pagination/FunPagination";
import { showToast } from "@/utils/financeUtils";
import { Add } from '@nutui/icons-react-taro';
import { Dialog, Menu, SafeArea, SearchBar } from "@nutui/nutui-react-taro";
import { useSetState } from "ahooks";
import { useRef, useState } from "react";
import { useIntl } from 'react-intl';
import CompleteTask from "../components/CompleteTask";
import EditTask from "../components/EditTask";
import TaskItem from "../components/TaskItem";
import { cancelTodo, completeTodo, createTodo, queryTodoList, updateTodo } from "../services";
import { CompleteTodoParams, CreateTodoParams, TodoEntity, UpdateTodoParams } from "../types";
import { StatusEnum } from "../types/todo.enum";

export default function TaskList() {
  const intl = useIntl();

  const statusPanelItems = [
    {
      keyStr: 'statuses',
      multiple: true,
      label: '',
      item: [
        {
          text: intl.formatMessage({ id: 'task.status.notCompleted' }),
          value: StatusEnum.NotCompleted,
        },
        {
          text: intl.formatMessage({ id: 'common.status.completed' }),
          value: StatusEnum.Completed,
        },
        {
          text: intl.formatMessage({ id: 'common.status.cancelled' }),
          value: StatusEnum.Cancelled,
        },
      ],
    },
  ];
  const statusRef = useRef<any>();
  const creatorRef = useRef<any>();
  const todoPersonRef = useRef<any>();
  const [params, setParams] = useState<Pick<Partial<TodoEntity>, 'taskDesc' | 'todoPerson' | 'createPerson'> & { statuses?: number[] }>({});


  const [state, setState] = useSetState({
    editVisible: false,
    completeVisible: false,
    currentTask: undefined as TodoEntity | undefined,
  });


  const setInputValue = (param) => {
    setParams((prevData) => ({ ...prevData, ...param }));
  };

  const fetchData = (query) => {
    return queryTodoList({ ...query, pageSize: 10 }).then(
      (result) => result?.data ?? [],
    );
  };

  const handleEdit = (record: TodoEntity) => {
    setState({ currentTask: record, editVisible: true });
  };


  const handleEditConfirm = async (data: CreateTodoParams | UpdateTodoParams) => {
    let result;
    if (state.currentTask) {
      result = await updateTodo(data as UpdateTodoParams);
    } else {
      result = await createTodo(data as CreateTodoParams);
    }
    if (result) {
      setState({ editVisible: false });
      showToast(intl.formatMessage({ id: 'common.message.submitSuccess' }));
      setParams({ ...params, timestamp: Date.now() } as any);
    }
  };

  const handleComplete = (record: TodoEntity) => {
    setState({ currentTask: record, completeVisible: true });
  };

  const handleCompleteConfirm = async (params: CompleteTodoParams) => {
    console.log(params)
    const result = await completeTodo(params);
    if (result) {
      showToast(intl.formatMessage({ id: 'task.message.taskCompleted' }));
      setState({ completeVisible: false });
      setParams({ ...params, timestamp: Date.now() } as any);
    }
  };

  const handleCancel = (record: TodoEntity) => {
    Dialog.open('comfirm', {
      title: intl.formatMessage({ id: 'common.button.confirm' }),
      content: intl.formatMessage({ id: 'task.dialog.cancel.content' }),
      onConfirm: () => {
        Dialog.close('comfirm')
        cancelTodo({ id: record.id }).then(() => {
          showToast(intl.formatMessage({ id: 'task.message.cancelSuccess' }));
          setParams({ ...params, timestamp: Date.now() } as any);
        })
      },
      onCancel: () => {
        Dialog.close('comfirm')
      },
    })
  };

  return <div className="flex flex-col h-screen">
    <CustomNavBar showBack={true} title={intl.formatMessage({ id: 'task.list.title' })} />

    <div className="pt-[16px] mb-[24px]">
      <SearchBar
        value={params.taskDesc}
        onChange={(val: string) => {
          setInputValue({ taskDesc: val });
        }}
        onClear={() => {
          setInputValue({ taskDesc: '' });
        }}
        placeholder={intl.formatMessage({ id: 'task.list.search.placeholder' })}
        right={<Add onClick={() => {
          setState({ currentTask: undefined, editVisible: true });
        }} />}
      />
    </div>
    <MenuWrap
      menu={
        <Menu>
          <Menu.Item title={intl.formatMessage({ id: 'common.label.creator' })} ref={creatorRef}>
            <FilterAccountPicker
              accountId={params.createPerson}
              onChange={(accountId) => {
                setParams({ ...params, createPerson: accountId });
                creatorRef.current?.toggle(false);
              }}
              label={intl.formatMessage({ id: 'common.label.creator' })}
            />
          </Menu.Item>
          <Menu.Item title={intl.formatMessage({ id: 'task.list.filter.todoPerson' })} ref={todoPersonRef}>
            <FilterAccountPicker
              accountId={params.todoPerson}
              onChange={(accountId) => {
                setParams({ ...params, todoPerson: accountId });
                todoPersonRef.current?.toggle(false);
              }}
              label={intl.formatMessage({ id: 'task.list.filter.todoPerson' })}
            />
          </Menu.Item>
          <Menu.Item title={intl.formatMessage({ id: 'task.list.filter.status' })} ref={statusRef}>
            <CustomMultipleChoose
              key={'statuses'}
              layout="vertical"
              onClose={() => {
                statusRef.current?.toggle(false);
              }}
              onConfirm={(e) => {
                setParams({ ...params, statuses: e.statuses });
              }}
              items={statusPanelItems}
            />
          </Menu.Item>
        </Menu>
      }
    />
    <div className="flex-1 min-h-0 overflow-scroll mt-[20px]">
      <FunPagination
        fetchData={fetchData}
        params={params}
        renderItem={(record, index) => <TaskItem
          record={record}
          className="bg-white rounded-[16px] mx-[28px] mb-[24px] px-[28px] py-[30px]"
          onEdit={handleEdit}
          onComplete={handleComplete}
          onCancel={handleCancel}
        />}
      />
    </div>


    <EditTask
      visible={state.editVisible}
      onClose={() => setState({ editVisible: false })}
      onConfirm={handleEditConfirm}
      editData={state.currentTask}
      title={state.currentTask ? intl.formatMessage({ id: 'task.edit.title.edit' }) : intl.formatMessage({ id: 'task.edit.title.create' })}
    />

    <CompleteTask
      visible={state.completeVisible}
      onClose={() => setState({ completeVisible: false })}
      onConfirm={handleCompleteConfirm}
      taskData={state.currentTask}
    />
    <Dialog id="comfirm" />
    <SafeArea position={'bottom'} />
  </div>;
}
