import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import { post as request } from '@/utils/request';
import type React from 'react';
import { GetItemGroupBaseRequest } from './types/get.item.group.base.request';
import { GoodsEntity, GoodsInfoByOEResult } from './types/GoodsEntity.entity';
import { IcItemGroupEntity, IcItemGroupListRequest } from './types/IcItemGroupType';
import { ItemGroupBase } from './types/item.group.base';
/**
 * 商品分页查询
 *
 * @param params
 * @returns
 */
export const queryGoodsPage = async (params: PageRequestParamsType) => {
  const { brandId, categoryId, ...rest } = params;
  rest.isFetchAllInventory = true;
  rest.isFetchSupplier = true;
  rest.isFetchWarehouseCostPrice = true;
  rest.isFetchAllCostPrice = true;
  if (brandId) {
    rest.brandIdList = [brandId];
  }
  if (categoryId) {
    rest.categoryIdList = categoryId;
  }
  return request<PageResponseDataType<GoodsEntity>>(`/ipmsconsole/goods/member/query`, {
    data: rest,
  });
};
/**
 * 商品sku分页查询
 *
 * @param params
 * @returns
 */
export const querySkuGoodsPage = async (params: PageRequestParamsType) => {
  return request<PageResponseDataType<GoodsEntity>>(`/ipmsgoods/item/sku/pageQuery`, {
    data: params,
  });
};


/**
 * 查询商品详情信息
 *
 * @param params
 * @returns
 */
export const goodsDetail = async (params: { itemId: React.Key }) => {
  return request<GoodsEntity>(`/ipmsgoods/item/detail`, {
    data: params,
  });
};
/**
 * 行业OE查询商品信息
 *
 * @param params
 * @returns
 */
export const queryGoodsListByOeNo = async (params: { oeNo: string }) => {
  return request<PageResponseDataType<GoodsInfoByOEResult>>(
    `ipmsgoods/goodsSearch/queryIndustryOeNo`,
    {
      data: params,
    },
  );
};

/**
 * 通用组基础信息查询
 */
export const getItemGroupBase = async (params: GetItemGroupBaseRequest): Promise<ItemGroupBase> => {
  return request(`/ipmsgoods/icItemGroupFacade/getItemGroupBase`, {
    data: params,
  });
};

/**
 * 通用分组列表
 */
export const queryIcItemGroupList = async (
  params: IcItemGroupListRequest,
): Promise<IcItemGroupEntity[]> => {
  return request(`/ipmsgoods/icItemGroupFacade/queryList`, {
    data: {
      ...params,
      isFetchBrand: true,
      isFetchBrandPart: true,
      isFetchOe: true,
      isFetchCategory: true,
      isFetchImage: true,
    },
  });
};

