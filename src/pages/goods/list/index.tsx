import notPic from '@/assets/not_pic.png';
import ChooseCategoryCard from '@/components/ChooseCategoryCard';
import CustomMultipleChoose from '@/components/CustomMultipleChoose';
import CustomNavBar from '@/components/CustomNavBar';
import CustomSearchBar from '@/components/CustomSearchBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import { queryGoodsPropertyPage } from '@/packages/stocks/inventory/services';
import RouterUtils from '@/utils/RouterUtils';
import { Menu, Price, SafeArea } from '@nutui/nutui-react-taro';
import { useSetState } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';
import ItemTagList from '../components/ItemTagList';
import { queryGoodsPage } from '../services';
import { GoodsEntity } from '../types/GoodsEntity.entity';

interface SearchParams {
  pageNo: number;
  pageSize: number;
  keyword?: string;
  groupId?: string;
}


const GoodsList: React.FC = () => {
  const [params, setParams] = useSetState<SearchParams>({
    pageNo: 1,
    pageSize: 10,
  });

  const [brandItems, setBrandItems] = useState<any>([]);
  const brandMenuRef = useRef<any>();
  const categoryMenuRef = useRef<any>();


  // 搜索配置
  const searchItems = [
    {
      key: 'queryKeyWord',
      name: '商品',
      scanShow: true,
      placeholder: '商品名称/编码/OE码',
    },
    {
      key: 'itemGroupName',
      name: '通用组',
      scanShow: false,
      placeholder: '通用组名称',
    },
  ];

  const defaultSearchItem = searchItems[0];

  useEffect(() => {
    queryGoodsPropertyPage({ pageNo: 1, pageSize: 1000, brandStatus: '1' }, 'brand').then(
      (result) => {
        setBrandItems([
          {
            keyStr: 'brandIdList',
            multiple: true,
            label: '',
            item: result.data?.map((s) => ({ text: s.brandName, value: s.brandId })),
          },
        ]);
      },
    );
  }, []);

  // 获取商品列表数据
  const fetchGoodsData = async (searchParams: SearchParams) => {
    try {
      const response = await queryGoodsPage(searchParams);
      return response?.data || [];
    } catch (error) {
      console.error('获取商品列表失败:', error);
      return [];
    }
  };


  // 根据搜索类型获取数据 - 商品数据
  const fetchGoodsDataForPagination = async (searchParams: SearchParams): Promise<GoodsEntity[]> => {
    return fetchGoodsData(searchParams);
  };

  const handleSearchInput = (param: any) => {
    setParams((prevData) => ({ ...prevData, ...param }));
  };

  // 渲染商品卡片
  const renderGoodsItem = (item: GoodsEntity) => {
    return (
      <div
        key={item.itemId}
        className="bg-white rounded-[16px] mx-[28px] my-[12px] p-[28px]"
        onClick={() => {
          RouterUtils.navigateTo({
            url: '/pages/goods/detail/index',
            params: { itemId: item.itemId },
          });
        }}
      >
        <div className="flex">
          {/* 商品图片 */}
          <img
            src={item.images?.[0] || notPic}
            className="w-[88px] h-[88px] rounded-[8px] bg-[#f5f5f5] mr-[24px] object-cover"
            alt={item.itemName}
          />

          {/* 商品信息 */}
          <div className="flex-1">

            {/* 商品名称 */}
            <div className="text-[32px] font-medium text-gray-900 mb-[12px] line-clamp-2">
              {item.itemName}
            </div>

            {/* 商品编码和基本信息 */}
            <div className="text-[24px] text-[#00000099] mb-[12px] flex">
              <ItemsWithDivider
                items={[item.itemSn, item.brandName, item.categoryName, item.unitName]}
              />
            </div>

            {/* 供应商编码和通用组 */}
            <div className="text-[24px] text-[#00000099] mb-[12px] leading-[1.5]">
              供应商编码 {item.brandPartNos?.[0] || '-'}
              {item.itemGroupRoList?.[0]?.name && (
                <>
                  <span className="mx-[12px]">通用组</span>
                  <span>{item.itemGroupRoList[0].name}</span>
                </>
              )}
            </div>

            {/* 价格信息 */}
            <div className="flex items-center mt-[12px] mb-[18px]">
              <Price
                price={item.suggestPrice || 0}
                size="normal"
                className="text-[28px] text-orange-500"
              />
              <span className="text-[24px] text-gray-600 ml-[12px]">
                (总库存 {item.inventoryNum || 0})
              </span>
            </div>
            {/* 标签 */}
            <div className="flex gap-[8px]">
              <ItemTagList itemStatus={item.itemStatus} itemTagList={item.itemTagList} />
            </div>
          </div>
        </div>
      </div>
    );
  };


  return (
    <div className="flex flex-col h-screen">
      {/* 导航栏 */}
      <CustomNavBar title="商品" />

      {/* 搜索栏 */}
      <div className="px-[28px] py-[16px]">
        <CustomSearchBar
          itemList={searchItems}
          defaultItem={defaultSearchItem}
          inputValue={handleSearchInput}
          isShowAdd={false}
        />
      </div>

      <MenuWrap
        menu={
          <Menu>
            <Menu.Item title="品牌" ref={brandMenuRef}>
              <CustomMultipleChoose
                isSearch={true}
                key={'brandId'}
                onClose={() => {
                  brandMenuRef.current?.toggle(false);
                }}
                onConfirm={(e) => {
                  setParams({ ...params, ...e });
                }}
                items={brandItems}
              />
            </Menu.Item>
            <Menu.Item title="品类" ref={categoryMenuRef}>
              <ChooseCategoryCard
                values={params.categoryIdList ?? []}
                onChange={(v, items) => {
                  setParams({ ...params, categoryIdList: v, categoryIdAndNameList: items });
                  categoryMenuRef.current?.toggle(false);
                }}
              />
            </Menu.Item>
          </Menu>
        }
      />



      {/* 商品列表 */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <FunPagination<GoodsEntity>
          params={params}
          fetchData={fetchGoodsDataForPagination}
          renderItem={(item) => renderGoodsItem(item)}
        />
      </div>

      <SafeArea position="bottom" />
    </div>
  );
};

export default GoodsList;