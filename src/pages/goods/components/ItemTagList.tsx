import { Tag } from "@nutui/nutui-react-taro";
import { GoodsEntity } from "../types/GoodsEntity.entity";

const ItemTagList = (props: {
  itemStatus: GoodsEntity['itemStatus']
  itemTagList: GoodsEntity['itemTagList']
}) => {
  const { itemTagList = [], itemStatus } = props;
  return (
    <>
      <Tag
        type={itemStatus === 1 ? 'success' : 'default'}
      >
        {itemStatus === 1 ? '已上架' : '已下架'}
      </Tag>
      {itemTagList?.slice(0, 3).map((tag, index) => (
        <Tag
          key={index}
          type="danger"
          color='#D1382BFF'
        >
          {tag.tagName}
        </Tag>
      ))}
      {itemTagList?.length > 3 && (
        <Tag
          type="danger"
          color='#D1382BFF'
        >
          +{itemTagList?.length - 3}
        </Tag>
      )}
    </>
  );
};

export default ItemTagList;