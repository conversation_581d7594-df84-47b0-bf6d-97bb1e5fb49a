import notPic from '@/assets/not_pic.png';
import Card from "@/components/Card";
import ItemsWithDivider from '@/components/ItemsWithDivider';
import { Divider, Tag } from "@nutui/nutui-react-taro";
import { useEffect, useState } from "react";
import { queryIcItemGroupList } from '../services';
import { IcItemGroupEntity } from '../types/IcItemGroupType';

const GroupInfo = (props) => {
  const { groupItems } = props;
  const [groupItemList, setGroupItemList] = useState<{
    groupId: string;
    groupName: string;
    itemList: IcItemGroupEntity[];
  }[]>([]);

  const queryGroupsItems = () => {
    Promise.all(groupItems.map((item) => {
      return queryIcItemGroupList({
        groupId: item.id.toString(),
      });
    })).then((result) => {
      console.log('result', result.map((itemList, index) => ({
        groupId: groupItems[index].id.toString(),
        groupName: groupItems[index].name,
        itemList
      })));
      setGroupItemList(
        result.map((itemList, index) => ({
          groupId: groupItems[index].id.toString(),
          groupName: groupItems[index].name,
          itemList
        })),
      );
    });
  };

  useEffect(() => {
    queryGroupsItems();
  }, [groupItems]);

  return (
    <>
      {groupItemList?.map((group) => (
        <Card key={group.groupId} className="mb-[24px]" title={group.groupName}>
          {
            group.itemList?.map((item, index) => (
              <div className='mb-2' key={item.id}>
                <div className="flex">
                  <img
                    src={item.images?.[0] || notPic}
                    className="w-[88px] h-[88px] rounded-[8px] bg-[#f5f5f5] mr-[24px] object-cover"
                    alt={item.itemName}
                  />
                  <div className="flex-1">
                    <div className="text-[28px] font-medium text-gray-900 mb-[12px]">
                      {item.itemName}
                    </div>
                    <div className="text-[24px] text-[#00000099] mb-[12px] flex">
                      <ItemsWithDivider
                        items={[item.itemSn, item.brandName, item.categoryName, item.unitName]}
                      />
                    </div>

                    {/* 供应商编码和通用组 */}
                    <div className="text-[24px] text-[#00000099] mb-[12px] leading-[1.5] flex gap-2">
                      <span>供应商编码 {item.brandPartNos?.[0] || '-'}</span>
                      <span>OE {item.oeNos?.[0] || '-'}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      {item.isMain === 1 && (
                        <Tag type="primary">主商品</Tag>
                      )}
                    </div>
                  </div>
                </div>

                {(group.itemList.length - 1) !== index && <Divider />}
              </div>
            ))
          }
        </Card>
      ))}
    </>
  );
};

export default GroupInfo;