import Card from '@/components/Card';
import CustomNavBar from '@/components/CustomNavBar';
import { SafeArea, Tabs } from '@nutui/nutui-react-taro';
import { useRouter } from '@tarojs/taro';
import React, { useEffect, useState } from 'react';
import ItemTagList from '../components/ItemTagList';
import { goodsDetail } from '../services';
import { GoodsEntity } from '../types/GoodsEntity.entity';
import BasicInfo from './BasicInfo';
import GroupInfo from './GroupInfo';
import StockInfo from './StockInfo';

const GoodsDetail: React.FC = () => {
  const router = useRouter();
  const { itemId } = router.params as { itemId: string; groupId?: string };

  const [goodsInfo, setGoodsInfo] = useState<GoodsEntity | null>(null);
  const [activeTab, setActiveTab] = useState('info');
  const [loading, setLoading] = useState(true);

  // 获取商品详情
  const fetchGoodsDetail = async () => {
    try {
      setLoading(true);
      const response = await goodsDetail({ itemId });
      setGoodsInfo(response);
    } catch (error) {
      console.error('获取商品详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (itemId) {
      fetchGoodsDetail();
    }
  }, [itemId]);

  // Tab配置
  const tabs = [
    { key: 'info', title: '商品信息' },
    { key: 'inventory', title: '库存分布' },
    { key: 'group', title: '通用组' },
    { key: 'detail', title: '商城详情' },
  ].filter((item) => {
    if (item.key === 'group') {
      return (goodsInfo?.itemGroupRoList || []).length > 0;
    }
    return true;
  });

  if (loading) {
    return (
      <div className="flex flex-col h-screen">
        <CustomNavBar title="商品详情" />
        <div className="flex-1 flex items-center justify-center">
          <div>加载中...</div>
        </div>
      </div>
    );
  }

  if (!goodsInfo) {
    return (
      <div className="flex flex-col h-screen">
        <CustomNavBar title="商品详情" />
        <div className="flex-1 flex items-center justify-center">
          <div>商品不存在</div>
        </div>
      </div>
    );
  }

  // 渲染商城详情
  const renderEcommerceDetail = () => (
    <Card>
      <div className="text-[28px] text-gray-900 leading-[1.6]" dangerouslySetInnerHTML={{ __html: goodsInfo.ecommerceDetail || '暂无商城详情' }} />
    </Card>
  );

  // 渲染Tab内容
  const renderTabContent = () => {
    switch (activeTab) {
      case 'info':
        return (
          <BasicInfo goodsInfo={goodsInfo} />
        );
      case 'inventory':
        return <StockInfo itemId={itemId} />;
      case 'group':
        return <GroupInfo groupItems={goodsInfo.itemGroupRoList ?? []} />
      case 'detail':
        return renderEcommerceDetail();
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <CustomNavBar title="商品详情" />

      <div className='px-[28px] py-[28px]'>
        <div className="text-[32px] font-medium mb-[12px] line-clamp-2">
          {goodsInfo.itemName}
        </div>
        <div className="flex gap-[8px] text-[26px] text-[#00000099] mb-[12px] items-center">
          {goodsInfo.itemSn}
          <ItemTagList itemStatus={goodsInfo.itemStatus} itemTagList={goodsInfo.itemTagList} />
        </div>
      </div>

      <div className="flex-1 min-h-0">
        <Tabs
          value={activeTab}
          onChange={(value) => setActiveTab(value as string)}
          tabStyle={{ position: 'sticky', top: '44px', zIndex: 110 }}
          style={{
            '--nutui-tabs-titles-background-color': 'white',
            '--nutui-tabs-titles-item-active-font-weight': 'Medium',
            '--nutui-tabs-titles-item-active-color': 'rgba(0,0,0,0.9)',
            '--nutui-tabs-tab-line-width': '64px',
            '--nutui-tabs-tabpane-backgroundColor': "transparent",
            '--nutui-tabs-tabpane-padding': "0px",
          }}
        >
          {tabs.map((tab) => (
            <Tabs.TabPane key={tab.key} title={tab.title} value={tab.key}>
              {renderTabContent()}
            </Tabs.TabPane>
          ))}
        </Tabs>
      </div>

      <SafeArea position="bottom" />
    </div >
  );
};

export default GoodsDetail;