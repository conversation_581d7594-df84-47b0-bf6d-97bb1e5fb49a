import { queryStockRecord } from '@/packages/sales/order/stocksPage/services';
import {
  QueryPurchaseRecordResponse,
  StockInventoryRo,
} from '@/packages/sales/order/stocksPage/types/query.purchase.record.response';
import { useEffect, useState } from 'react';

import Card from '@/components/Card';
import blueIcon from '@/packages/sales/order/stocksPage/imgs/blue.svg';
import greenIcon from '@/packages/sales/order/stocksPage/imgs/green.svg';
import redIcon from '@/packages/sales/order/stocksPage/imgs/red.svg';
import { IconFont } from '@nutui/icons-react-taro';

const StockInfo = (props) => {
  const { itemId } = props;

  const [detail, setDetail] = useState<QueryPurchaseRecordResponse>();

  useEffect(() => {
    if (itemId) {
      queryStockRecord({
        itemIdList: [itemId],
      }).then((result) => {
        if (result) {
          setDetail(result);
        }
      });
    }
  }, [itemId]);

  if (!detail) {
    return null;
  }

  const stocksInfo = [
    {
      label: '总库存',
      value: detail?.totalInventoryNum ?? 0,
      icon: blueIcon,
    },
    {
      label: '占用库存',
      value: detail?.totalLockedNum ?? 0,
      icon: redIcon,
    },
    {
      label: '可用库存',
      value: detail?.totalAvaNum ?? 0,
      icon: greenIcon,
    },
  ];

  const detailData = (record: StockInventoryRo) => [
    {
      label: '总库存',
      value: record.inventoryNum,
    },
    {
      label: '占用库存',
      value: record.lockedNum,
    },
    {
      label: '可用库存',
      value: record.avaNum,
    },
    {
      label: '采购在途',
      value: record.purchaseTransitNum,
    },
    {
      label: '调拨在途',
      value: record.transitNum,
    },
    {
      label: '库存上限/下限',
      value: `${record.upperLimit ?? '-'}/${record.lowerLimit ?? '-'}`,
    },
  ];

  return (
    <div>
      <Card className='flex '>
        {stocksInfo?.map((item) => (
          <div className="flex-1 flex flex-col items-center !mx-[0px] !px-0 !mb-0">
            <IconFont name={item.icon} style={{ width: '40px', height: '40px' }} />
            <div className="text-[34px] my-[10px] font-medium break-all text-center">
              {item.value}
            </div>
            <div className="text-secondary text-[28px] mt-1">{item.label}</div>
          </div>
        ))}
      </Card>
      {detail?.stockInventoryRos?.map((item) => (
        <Card
          title={
            <div>
              {item.warehouseName}
            </div>
          }
        >
          <div className="flex flex-wrap">
            {detailData(item).map((item) => (
              <div className="w-[33%] flex flex-col items-center my-[12px]">
                <div className="text-[28px] text-thirdary mb-[16px]">{item.label}</div>
                <div className="text-[32px]">{item.value}</div>
              </div>
            ))}
          </div>
        </Card>
      ))}
    </div>
  );
};

export default StockInfo;
