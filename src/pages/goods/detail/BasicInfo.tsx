import Card from '@/components/Card';
import DetailList from "@/components/DetailList";
import { Image, ImagePreview } from '@nutui/nutui-react-taro';
import { useState } from 'react';


const BasicInfo = (props) => {
  const { goodsInfo = {} } = props;
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [init, setInit] = useState(0);
  return (
    <div className="space-y-[24px]">

      {/* 商品图片和基本信息 */}
      <Card>
        <DetailList
          colon
          labelColor="text-[#00000099]"
          dataSource={[
            { label: '供应商编码', value: goodsInfo.brandPartNos?.[0] },
            { label: 'OE', value: goodsInfo.oeNos?.[0] },
            { label: '品牌', value: goodsInfo.brandName },
            { label: '分类', value: goodsInfo.categoryName },
            { label: '单位', value: goodsInfo.unitName },
            { label: '备注', value: goodsInfo.remark },
            {
              label: '图片', value: <>
                {
                  goodsInfo.images && goodsInfo.images.length > 0 && (
                    <div className="flex gap-[16px]">
                      {goodsInfo.images.map((image, index) => (
                        <Image
                          key={index}
                          src={image}
                          width={40}
                          height={40}
                          className="rounded-[8px]"
                          onClick={() => {
                            setInit(index);
                            setShowPreview(true);
                          }}
                        />
                      ))}
                    </div>
                  )
                }
              </>
            },
          ]}
        />
      </Card>

      <Card title="价格信息">
        <div className="flex justify-around items-center">
          <div className="flex flex-col text-center">
            <span className="text-[28px] text-[#00000073] mb-2">销售价</span>
            <span>
              {(goodsInfo.suggestPrice || 0).toFixed(2)}
            </span>
          </div>
          <div className="flex flex-col text-center">
            <span className="text-[28px] text-[#00000073] mb-2">最低售价</span>
            <span>
              {(goodsInfo.lowPrice || 0).toFixed(2)}
            </span>
          </div>
          <div className="flex flex-col text-center">
            <span className="text-[28px] text-[#00000073] mb-2">成本价</span>
            <span>
              {(goodsInfo.costPrice || 0).toFixed(2)}
            </span>
          </div>
        </div>
      </Card>


      <Card title="采购信息">
        <div className="space-y-[24px]">
          {goodsInfo.supplierList?.map((supplier, index) => (
            <div key={index}>
              <div className="flex justify-between items-center text-[28px]">
                <span>供应商{index + 1}</span>
                <span>{(supplier.purchasePrice || 0).toFixed(2)}</span>
              </div>
            </div>
          ))}
        </div>
      </Card>


      <Card title="其他信息">
        <DetailList colon labelColor="text-[#00000099]" dataSource={[
          { label: '销售渠道', value: goodsInfo.channelCodeList?.[0]?.text || '-' },
          { label: '规格', value: goodsInfo.spec || '-' },
          { label: '真实品牌', value: goodsInfo.brandName || '-' },
          { label: '真实供应商编码', value: goodsInfo.brandPartNos?.[0] || '-' },
          { label: '订货编码', value: goodsInfo.itemSn || '-' },
          { label: '商品条码', value: goodsInfo.barCode || '-' },
          { label: '产地', value: goodsInfo.originRegionName || '-' },
          { label: '车型备注', value: goodsInfo.adaptModel || '-' },
        ]} />
      </Card>

      <ImagePreview
        autoPlay
        images={goodsInfo.images.map(item => ({ src: item }))}
        visible={showPreview}
        onClose={() => setShowPreview(false)}
        defaultValue={init}
      />
    </div >
  );
};

export default BasicInfo;