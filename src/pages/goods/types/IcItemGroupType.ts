export interface IcItemGroupListRequest {
  /**
   * param
   */
  firstName?: string;
  /**
   * 分组id
   */
  groupId?: string;
  /**
   * 是否获取品牌
   */
  isFetchBrand?: boolean;
  /**
   * 是否获取供应商编码
   */
  isFetchBrandPart?: boolean;
  /**
   * 是否获取类目
   */
  isFetchCategory?: boolean;
  /**
   * 是否获取oe
   */
  isFetchOe?: boolean;
  /**
   * 是否获取单位
   */
  isFetchUnit?: boolean;
  /**
   * param
   */
  lastName?: string;
  /**
   * param
   */
  memberId?: string;
  /**
   * param
   */
  operatorName?: string;
  /**
   * param
   */
  operatorNo?: string;
}

/**
 * 通用分组列表
 */
export interface IcItemGroupEntity {
  /**
   * 商品标签展示，用于搜索列表页，及专题页商品模块展示
   */
  activityTag?: string;
  /**
   * 适用车型
   */
  adaptModel?: string;
  /**
   * etc适配车型id列表
   */
  adaptModelIds?: string[];
  /**
   * 适配车系
   */
  adaptSeries?: string;
  /**
   * 适配车系code
   */
  adaptSeriesCode?: string;
  /**
   * 商品条形码
   */
  barCode?: string;
  /**
   * 品牌ID
   */
  brandId?: string;
  /**
   * 品牌
   */
  brandName?: string;
  /**
   * 供应商编码列表
   */
  brandPartNos?: string[];
  /**
   * 品牌简称
   */
  brandShortName?: string;
  /**
   * 类目ID
   */
  categoryId?: string;
  /**
   * 三级类目名称
   */
  categoryName?: string;
  /**
   * 商品销售渠道,参考：ChannelCodeEnum
   */
  channelCodeList?: number[];
  /**
   * 商品详情
   */
  description?: string;
  /**
   * 尺寸单位,单位是cm还是m
   */
  dimensionUnit?: string;
  /**
   * 商城详情
   */
  ecommerceDetail?: string;
  /**
   * ETC号
   */
  etcNo?: string;
  /**
   * 通用组id
   */
  groupId: string;
  /**
   * 通用组名称
   */
  groupName?: string;
  /**
   * 明细detailId
   */
  id?: string;
  /**
   * 图片列表
   */
  images?: string[];
  /**
   * 是否现货:0-否1-是
   */
  inStocks?: number;
  /**
   * 是否为主商品，0:否，1:是
   */
  isMain?: number;
  /**
   * 是否套装：0-否1-是
   */
  isSuit?: number;
  /**
   * 通用组信息列表
   */
  itemGroupDetails?: ItemGroupDetail[];
  /**
   * 通用组id列表
   */
  itemGroupIdList?: string[];
  /**
   * 通用组列表
   */
  itemGroupRoList?: ItemGroupRoList[];
  /**
   * 商品ID
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 状态:0-禁用1-启用
   */
  itemStatus?: number;
  /**
   * 标签信息列表
   */
  itemTagList?: ItemTagList[];
  /**
   * 0-标准1-非标
   */
  itemType?: number;
  /**
   * 最低限价
   */
  lowPrice?: number;
  /**
   * 采购库存上限
   */
  maxPurchaseStock?: number;
  /**
   * 门店零售商id
   */
  memberId?: string;
  /**
   * 助记码
   */
  memCode?: string;
  /**
   * 采购库存下限
   */
  minPurchaseStock?: number;
  /**
   * OE列表
   */
  oeNos?: string[];
  /**
   * 订货编码
   */
  orderCode?: string;
  /**
   * 产地id
   */
  originRegionId?: string;
  /**
   * 产地
   */
  originRegionName?: string;
  /**
   * 自编码
   */
  ownCode?: string;
  /**
   * 定价详情
   */
  priceDetails?: PriceDetail[];
  /**
   * 活动价
   */
  promotionPrice?: number;
  /**
   * 活动价
   */
  promotionPriceTax?: number;
  /**
   * 采购状态:0-否1-是,没用到
   */
  purchaseStatus?: number;
  /**
   * 真实品牌
   */
  realBrandName?: string;
  /**
   * 真实供应商编码
   */
  realBrandPartNo?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 日均销量
   */
  saleAvgNum?: number;
  /**
   * 30天销量
   */
  saleNum30d?: number;
  /**
   * 销售状态:0-否1-是,没用到
   */
  saleStatus?: number;
  /**
   * 高度
   */
  skuHeight?: number;
  /**
   * skuId
   */
  skuId?: string;
  /**
   * 长度
   */
  skuLength?: number;
  /**
   * SKU商品名称
   */
  skuName?: string;
  /**
   * 商品备注
   */
  skuRemark?: string;
  /**
   * 重量,单位kg'
   */
  skuWeight?: number;
  /**
   * 宽度
   */
  skuWidth?: number;
  /**
   * 三方数据编码
   */
  sourceCode?: string;
  /**
   * 商品规格
   */
  spec?: string;
  /**
   * 建议售价
   */
  suggestPrice?: number;
  /**
   * 供应商列表
   */
  supplierList?: SupplierList[];
  /**
   * 三方商品号
   */
  thirdNo?: string;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位
   */
  unitName?: string;
}

export interface ItemGroupDetail {
  /**
   * param
   */
  createPerson?: string;
  /**
   * param
   */
  createTime?: string;
  /**
   * 通用组id
   */
  groupId?: string;
  /**
   * 明细detailId
   */
  id?: string;
  /**
   * param
   */
  isDelete?: number;
  /**
   * 是否为主商品，0:否，1:是
   */
  isMain?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * param
   */
  memberId?: string;
  /**
   * 通用组名称
   */
  name?: string;
  /**
   * param
   */
  updatePerson?: string;
  /**
   * param
   */
  updateTime?: string;
}

export interface ItemGroupRoList {
  /**
   * 主键id
   */
  id?: number;
  /**
   * 通用组名称
   */
  name?: string;
  /**
   * 0禁用1启用
   */
  state?: number;
  /**
   * 调货建议按主商品合并，0:否，1是
   */
  transferSuggest?: number;
}

export interface ItemTagList {
  /**
   * param
   */
  createPerson?: string;
  /**
   * param
   */
  createTime?: string;
  /**
   * param
   */
  isDelete?: number;
  /**
   * param
   */
  memberId?: string;
  /**
   * 0:不对客显示,1:对客显示
   */
  showFlag?: number;
  /**
   * 0:不对客显示,1:对客显示
   */
  showFlagName?: string;
  /**
   * 标签id
   */
  tagId?: string;
  /**
   * 标签名称
   */
  tagName?: string;
  /**
   * 0=启用1=禁用
   */
  tagStatus?: number;
  /**
   * 0=启用1=禁用
   */
  tagStatusName?: string;
  /**
   * param
   */
  updatePerson?: string;
  /**
   * param
   */
  updateTime?: string;
}

export interface PriceDetail {
  /**
   * 商品ID
   */
  itemId?: string;
  /**
   * 分层定价id
   */
  levelId?: string;
  /**
   * 分层定价名称
   */
  levelName?: string;
  /**
   * 价格
   */
  levelPrice?: number;
  /**
   * 门店零售商id
   */
  memberId?: string;
}

export interface SupplierList {
  /**
   * 是否默认供货商
   */
  isDefault?: number;
  /**
   * 商品ID
   */
  itemId?: number;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 最小起订量
   */
  moq?: number;
  /**
   * 最小包装量
   */
  mpq?: number;
  /**
   * 操作（1-新增2-删除）
   */
  operation?: number;
  /**
   * 供应商采购价
   */
  purchasePrice?: number;
  /**
   * 供应商ID
   */
  supplierId?: string;
  /**
   * 供应商名称
   */
  supplierName?: string;
}
