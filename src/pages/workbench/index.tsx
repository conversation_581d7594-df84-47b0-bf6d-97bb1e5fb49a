import Card from '@/components/Card';
import CustomNavBar from '@/components/CustomNavBar';
import CustomTabBar from "@/components/CustomTabBar";
import { Image } from '@tarojs/components';
import { navigateTo, useLoad } from '@tarojs/taro';
import PermissionComponent from '../splash/PermissionComponent';
import afterSales from './images/after_sales.png';
import check from './images/check.png';
import collection from './images/collection.png';
import customerList from './images/customer_list.png';
import delivery from './images/delivery.png';
import inventory from './images/inventory.png';
import orderManagement from './images/order_management.png';
import outbound from './images/outbound.png';
import payment from './images/payment.png';
import products from './images/products.png';
import salesInvoicing from './images/sales_invoicing.png';
import salesReturns from './images/sales_returns.png';
import warehousing from './images/warehousing.png';
import './index.scss';

export default function Index() {
  // 获取系统信息来设置自定义导航栏的高度
  useLoad(() => {
    console.log('Page loaded.');
  });

  const icons = {
    客户列表: {
      imageUrl: customerList,
      url: '/packages/customer/list/index',
      permission: '/customer/list/view',
    },
    商品查询: {
      imageUrl: products,
      url: '/pages/goods/list/index',
      // permission: '/goods/list/view',
    },
    销售开单: {
      imageUrl: salesInvoicing,
      url: '/packages/sales/order/edit/index',
      permission: '/sales/order/edit/view',
    },
    销售退货: {
      imageUrl: salesReturns,
      url: '/packages/sales/returns/operation/index',
      permission: '/sales/returns/operation/view',
    },
    销售管理: {
      imageUrl: orderManagement,
      url: '/packages/sales/order/list/index',
      permission: '/sales/order/detail/view',
    },
    退货管理: {
      imageUrl: afterSales,
      url: '/packages/sales/returns/list/index',
      permission: '/sales/returns/list/view',
    },
    出库管理: {
      imageUrl: outbound,
      url: '/packages/stocks/output/list/index',
      permission: '/stocks/output/view',
    },
    入库管理: {
      imageUrl: warehousing,
      url: '/packages/stocks/input/list/index',
      permission: '/stocks/input/view',
    },
    盘点管理: {
      imageUrl: check,
      url: '/packages/stocks/check/list/index',
      permission: '/stocks/check/view',
    },
    库存管理: {
      imageUrl: inventory,
      url: '/packages/stocks/inventory/list/index',
      permission: '/stocks/inventory/view',
    },
    收款: {
      imageUrl: collection,
      url: '/packages/finance/receive/list/index',
      permission: '/finance/receive/view',
    },
    付款: {
      imageUrl: payment,
      url: '/packages/finance/payment/list/index',
      permission: '/finance/payment/view',
    },
    配送任务: {
      imageUrl: delivery,
      url: '/packages/stocks/delivery/list/index',
      // permission: '/stocks/delivery/view',
    },
  };

  // 单个图标项组件
  const IconItem = ({ name, imageUrl, url, permission }) => (
    <PermissionComponent permission={permission}>
      <div className="flex flex-col items-center" onClick={() => navigateTo({ url })}>
        <Image src={imageUrl} style={{ width: '22px', height: '22px' }} />
        <div className="text-center text-[28px] pt-[20px] text-[#666666]">{name}</div>
      </div>
    </PermissionComponent>
  );

  // 封装组件
  const IconComponent = ({ title, items }) => (
    <Card title={title}>
      <div className="grid grid-cols-4 gap-4">
        {items.map((item, index) => (
          <IconItem
            key={index}
            name={item.name}
            imageUrl={icons[item.name]?.imageUrl}
            url={icons[item.name]?.url}
            permission={icons[item.name]?.permission}
          />
        ))}
      </div>
    </Card>
  );

  const stockItems = [
    { name: '配送任务' },
    { name: '库存管理' },
  ];
  const salesItems = [
    { name: '销售开单' },
    // { name: '销售退货' },
    { name: '销售管理' },
    // { name: '退货管理' },
  ];
  return (
    <div>
      <CustomNavBar title={'工作台'} showBack={false} />
      <IconComponent title="客户" items={[{ name: '客户列表' }]} />
      <IconComponent title="商品" items={[{ name: '商品查询' }]} />
      <IconComponent title="销售" items={salesItems} />
      <IconComponent title="仓储" items={stockItems} />
      <IconComponent title="财务" items={[{ name: '收款' }, /**{ name: '付款' }**/]} />
      <CustomTabBar activeTabIndex={1} />
    </div>
  );
}
