import { queryStore } from "@/components/ChooseStoreAndWarehouseModal/services";
import { warehouseList } from "@/components/ChooseWarehouse/services";
import { OrderStatus } from "@/packages/sales/order/list/types/OrderStatus";
import { queryInventoryPagePost } from "@/packages/stocks/inventory/services";
import { ArrowDown } from "@nutui/icons-react-taro";
import { Picker, PickerOption } from "@nutui/nutui-react-taro";
import { navigateTo } from "@tarojs/taro";
import { useAsyncEffect, useReactive } from "ahooks";
import { defaultTo, forEach } from "lodash";
import { useState } from "react";
import block_bg from '../../imgs/block_bg.png';
import { queryOrderStatusCount } from "../../services";
import { StoreRequestParams } from "../../types/StoreRequestParams";

const TodoComponent = (props: { reloadFlag: number }) => {
  const { reloadFlag } = props;
  const [chooseStoreVisible, setChooseStoreVisible] = useState(false);
  const [storeId, setStoreId] = useState<string>('');
  const [storeName, setStoreName] = useState<string>();
  const [storeIdListParams, setStoreIdListParams] = useState<StoreRequestParams>();
  const [storeOptions, setStoreOptions] = useState<PickerOption[]>([]);


  // 待办事项
  const todoMap = useReactive<Record<string, number>>({
    // 待处理订单
    WillHandle: 0,
    // 待出库订单
    OutBound: 0,
    // 库存预警
    StockWarning: 0,
  });
  // 查询待处理/到出库订单统计数据
  const queryOrderStatusCountFn = () => {
    queryOrderStatusCount({
      ...storeIdListParams,
    }).then((result) => {
      const res = result as Record<OrderStatus, number>;
      todoMap.OutBound = defaultTo(res[OrderStatus.WAIT_TO_OUTBOUND], 0);
      todoMap.WillHandle = defaultTo(res[OrderStatus.WAIT_TO_HANDLE], 0);
    });
  };


  const queryStoreFn = () => {
    queryStore({ status: 1 }).then((storeResult) => {
      if (storeResult) {
        setStoreOptions(
          storeResult.map((store) => ({ value: store.id ?? '', text: store.name ?? '' })),
        );
      }
    });
  };

  useAsyncEffect(async () => {
    // 查询门店数据
    queryStoreFn();
  }, [reloadFlag]);

  // 待出库
  useAsyncEffect(async () => {
    queryOrderStatusCountFn();
  }, [storeIdListParams, storeIdListParams, reloadFlag]);

  // 查询库存预警服务
  const queryInventoryPagePostFn = () => {
    queryInventoryPagePost({
      onlyTotalStatistics: true,
      invLimitStatusList: [1, 2],
      storeId: storeId == '0' ? undefined : storeId,
    }).then((result) => {
      todoMap.StockWarning = defaultTo(result?.total, 0);
    });
  };
  // 库存预警
  useAsyncEffect(async () => {
    queryInventoryPagePostFn();
  }, [storeId, reloadFlag]);



  return <>
    <div className="bg-white rounded-[16px] mx-[28px] px-[28px] py-[30px] my-2" style={{
      background: `url(${block_bg}) white no-repeat`,
      backgroundSize: 'contain',
      border: '1px solid #fff',
    }}>
      <div className="flex justify-between items-center">
        <span className="text-[32px] font-medium">待办事项</span>
        <span
          className="text-[28px] text-[#666] flex items-center gap-[8px]"
          onClick={() => setChooseStoreVisible(true)}
        >
          {storeName ?? '全部门店'}
          <ArrowDown className="top-[5px] ml-[8px]" />
        </span>
      </div>

      <div className="flex justify mt-[28px] my-[28px] text-block">
        <div
          className="flex flex-col items-center w-1/3"
          onClick={() =>
            navigateTo({
              url: '/packages/sales/order/list/index?initOrderStatus=90&initStoreId=' + storeId,
            })
          }
        >
          <div className="text-[32px]">{todoMap.WillHandle}</div>
          <div className="text-[28px] opacity-60 pt-[15px]">待处理订单</div>
        </div>
        <div
          className="flex flex-col items-center w-1/3"
          onClick={() =>
            navigateTo({
              url:
                '/packages/sales/order/list/index?initOrderStatus=300&initStoreId=' + storeId,
            })
          }
        >
          <div className="text-[32px]">{todoMap.OutBound}</div>
          <div className="text-[28px] opacity-60 pt-[15px]">待出库订单</div>
        </div>
        <div
          className="flex flex-col items-center w-1/3"
          onClick={async () => {
            // 根据门店ID查询所有的仓库
            if (storeId) {
              const { warehouseSimpleRoList } = await warehouseList({ storeIdList: [storeId] });
              if (warehouseSimpleRoList) {
                const initWarehouseIdList: string[] = [];
                forEach(warehouseSimpleRoList, (value) => {
                  initWarehouseIdList.push(value.id!);
                });
                navigateTo({
                  url:
                    '/packages/stocks/inventory/list/index?initInvLimitStatusList=1,2&initWarehouseIdList=' +
                    initWarehouseIdList,
                }).then();
                return;
              }
            }
            navigateTo({
              url: '/packages/stocks/inventory/list/index?initInvLimitStatusList=1,2',
            }).then();
          }}
        >
          <div className="text-[32px]">{todoMap.StockWarning}</div>
          <div className="text-[28px] opacity-60 pt-[15px]">库存预警</div>
        </div>
      </div>
    </div>
    <Picker
      title="请选择门店"
      visible={chooseStoreVisible}
      options={[{ value: '', text: '全部门店' }, ...storeOptions]}
      onConfirm={(options, values) => {
        options.forEach((option: any) => {
          setStoreId(option.value);
          setStoreName(option.text);
          setStoreIdListParams({ storeIdList: option.value ? [option.value] : [] });
        });
      }}
      onClose={() => setChooseStoreVisible(false)}
    />
  </>
}
export default TodoComponent