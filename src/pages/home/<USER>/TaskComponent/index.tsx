import CompleteTask from "@/pages/task/components/CompleteTask";
import EditTask from "@/pages/task/components/EditTask";
import TaskItem from "@/pages/task/components/TaskItem";
import { cancelTodo, completeTodo, createTodo, queryTodoList, updateTodo } from "@/pages/task/services";
import { CompleteTodoParams, CreateTodoParams, TodoEntity, UpdateTodoParams } from "@/pages/task/types";
import { showToast } from "@/utils/financeUtils";
import { Add, ArrowRight } from '@nutui/icons-react-taro';
import { Button, Dialog, Divider } from "@nutui/nutui-react-taro";
import Taro from "@tarojs/taro";
import { useSetState } from "ahooks";
import { useEffect, useState } from "react";
import { useIntl } from 'react-intl';
import block_bg from '../../imgs/block_bg.png';



export default function TaskComponent(props: {
  reloadFlag: number;
}) {
  const intl = useIntl();
  const { reloadFlag } = props;
  const [data, setData] = useState<TodoEntity[]>([]);

  const [state, setState] = useSetState({
    editVisible: false,
    completeVisible: false,
    currentTask: undefined as TodoEntity | undefined,
  });


  useEffect(() => {
    fetchData({});
  }, [reloadFlag]);

  const fetchData = (query) => {
    return queryTodoList({ ...query, pageSize: 10 }).then(
      (result) => setData(result?.data ?? []),
    );
  };

  const handleEdit = (record: TodoEntity) => {
    setState({ currentTask: record, editVisible: true });
  };


  const handleEditConfirm = async (data: CreateTodoParams | UpdateTodoParams) => {
    let result;
    if (state.currentTask) {
      result = await updateTodo(data as UpdateTodoParams);
    } else {
      result = await createTodo(data as CreateTodoParams);
    }
    if (result) {
      setState({ editVisible: false });
      showToast(intl.formatMessage({ id: 'common.message.submitSuccess' }));
      fetchData({});
    }
  };

  const handleComplete = (record: TodoEntity) => {
    setState({ currentTask: record, completeVisible: true });
  };

  const handleCompleteConfirm = async (params: CompleteTodoParams) => {
    console.log(params)
    const result = await completeTodo(params);
    if (result) {
      showToast(intl.formatMessage({ id: 'task.message.taskCompleted' }));
      setState({ completeVisible: false });
      fetchData({});
    }
  };

  const handleCancel = (record: TodoEntity) => {
    Dialog.open('comfirm', {
      title: intl.formatMessage({ id: 'common.button.confirm' }),
      content: intl.formatMessage({ id: 'task.dialog.cancel.content' }),
      onConfirm: () => {
        Dialog.close('comfirm')
        cancelTodo({ id: record.id }).then(() => {
          showToast(intl.formatMessage({ id: 'task.message.cancelSuccess' }));
          fetchData({});
        })
      },
      onCancel: () => {
        Dialog.close('comfirm')
      },
    })
  };

  return <div className="bg-white rounded-[16px] mx-[28px] px-[28px] py-[30px] my-2" style={{
    background: `url(${block_bg}) white no-repeat`,
    backgroundSize: 'contain',
    border: '1px solid #fff',
  }}>
    <div className="flex justify-between items-center">
      <span className="text-[32px] font-medium">任务列表</span>
      <span
        className="text-[28px] text-[#666] flex items-center gap-[8px]"
        onClick={() => {
          Taro.navigateTo({ url: '/pages/task/list/index' });
        }}
      >
        全部任务
        <ArrowRight color="#666" height="16px" width="16px" />
      </span>
    </div>

    <div className="my-2">
      <Button block
        onClick={() => {
          setState({ currentTask: undefined, editVisible: true });
        }}
      ><Add className="mr-2" width={15} height={15} color="#000000CC" />新建待办</Button>
    </div>

    {
      data.map((item, index) => <>
        <TaskItem
          className="py-[30px]"
          key={index}
          record={item}
          onEdit={handleEdit}
          onComplete={handleComplete}
          onCancel={handleCancel}
        />
        {index < data.length - 1 && <Divider />}
      </>)
    }

    <EditTask
      visible={state.editVisible}
      onClose={() => setState({ editVisible: false })}
      onConfirm={handleEditConfirm}
      editData={state.currentTask}
      title={state.currentTask ? intl.formatMessage({ id: 'task.edit.title.edit' }) : intl.formatMessage({ id: 'task.edit.title.create' })}
    />

    <CompleteTask
      visible={state.completeVisible}
      onClose={() => setState({ completeVisible: false })}
      onConfirm={handleCompleteConfirm}
      taskData={state.currentTask}
    />
    <Dialog id="comfirm" />
  </div>;
}
