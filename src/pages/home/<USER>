body {
  background: url('../../assets/bgTop.png') #f5f5f5 no-repeat fixed;
  background-size: contain;
}
.header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  box-sizing: content-box;
  width: 100%;
  padding-top: calc(env(safe-area-inset-top) + 44px);
  background-image: url('../../assets/bgTop.png');
  background-repeat: no-repeat;
  background-size: cover;
}
.sale-amount-background {
  background-image: url('./imgs/saleamountbg.svg');
  background-position: center;
  background-size: cover;
}

.msg-background {
  background-image: url('./imgs/messagebg.png');
  background-position: center;
  background-size: cover;
}

.todo-background {
  background-image: url('./imgs/todobg.png');
  background-position: center;
  background-size: cover;
}

.custom-price {
  color: #ffffff;
  font-size: 32px;
}

.custom-price-last {
  color: #ffffff;
  font-weight: lighter;
}

.today-month {
  --nutui-radio-button-border-radius: 4px;
  --nutui-radiogroup-radio-margin: -1px;
  --nutui-radio-button-active-border: 1px solid #ffffff;
  --nutui-radio-label-color: #ffffff;
}

.no-wrap {
  overflow: hidden;
  white-space: nowrap;
  /* 隐藏溢出内容 */
  text-overflow: ellipsis;
  /* 当内容溢出时，在结尾添加省略号 */
}

.msg-divider {
  bordercolor: 'rgba(0,0,0,0.08)';
}

.msgUnRead {
  width: 12px;
  height: 12px;
  background-color: #f83431;
  border-radius: 24px;
}
