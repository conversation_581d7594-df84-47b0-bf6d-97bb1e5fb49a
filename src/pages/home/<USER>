import CustomTabBar from '@/components/CustomTabBar';
import { queryMsgList } from '@/packages/message/services';
import { MsgStatus } from '@/packages/message/types/MsgStatus';
import { MsgListItemEntity } from '@/packages/message/types/msg.list.item.entity';
import QuickActionsComponent from '@/pages/home/<USER>/QuickActionsComponent';
import logoShare from '@/pages/login/images/logo-share.png';
import RegUtils from '@/utils/RegUtils';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Image, PullToRefresh } from '@nutui/nutui-react-taro';
import { navigateTo, useShareAppMessage } from '@tarojs/taro';
import { useAsyncEffect, useToggle } from 'ahooks';
import dayjs from 'dayjs';
import { useState } from 'react';
import TaskComponent from './components/TaskComponent';
import TodoComponent from './components/TodoComponent';
import Logo from './imgs/GRIPX_LOGO.png';
import messagebg from './imgs/messagebg.png';
import './index.scss';


export default function Index() {
  const [state, { toggle }] = useToggle(false);
  const [dataType, setDataType] = useState<string>('DAY');
  const [saleGrossProfit, setSaleGrossProfit] = useState<string[]>();
  const [saleOrderNum, setSaleOrderNum] = useState<string[]>();
  const [fundsReceived, setFundsReceived] = useState<string[]>();
  const [saleAmount, setSaleAmount] = useState<string[]>();
  const [msgList, setMsgList] = useState<MsgListItemEntity[]>();
  const [isReloadFlag, setIsReloadFlag] = useState<number>();

  useShareAppMessage(() => {
    return {
      imageUrl: logoShare,
      path: '/pages/splash/index',
    };
  });

  // 查询消息列表服务
  const queryMsgListFn = () => {
    queryMsgList({ pageNo: 1, pageSize: 1, isDelete: 0 }).then((result) => {
      if (result?.data) {
        setMsgList(result.data);
      }
    });
  };

  useAsyncEffect(async () => {
    // 查询消息列表
    queryMsgListFn();
  }, []);

  // 查询实时概况服务
  // const queryIndexOverviewListFn = () => {
  //   queryIndexOverviewList({
  //     type: dataType,
  //     ...storeIdListParams,
  //   }).then((result) => {
  //     if (isEmpty(result)) return;
  //     const [yesterday, today] = result;
  //     setSaleAmount([today?.saleAmount ?? '', yesterday?.saleAmount ?? '']);
  //     setSaleGrossProfit([today?.saleGrossProfit ?? '', yesterday?.saleGrossProfit ?? '']);
  //     setSaleOrderNum([today?.saleOrderNum ?? '', yesterday?.saleOrderNum ?? '']);
  //     setFundsReceived([today?.fundsReceived ?? '', yesterday?.fundsReceived ?? '']);
  //   });
  // };

  // 实时概况
  // useAsyncEffect(async () => {
  //   queryIndexOverviewListFn();
  // }, [dataType, storeIdListParams]);



  // 下拉刷新数据
  const pullToRefresh = async () => {
    queryMsgListFn();
    setIsReloadFlag(dayjs().valueOf());
  };

  return (
    <>
      <>
        <div className='px-[28px] header'>
          <Image src={Logo} width={104} height={32} />
        </div>
        <div style={{ height: 62 }}></div>
      </>
      <PullToRefresh onRefresh={() => pullToRefresh()}>
        {/*快捷操作*/}
        <QuickActionsComponent
          items={[
            { name: '销售开单' },
            { name: '库存管理' },
            { name: '收款' },
            { name: '配送任务' },
          ]}
        />
        {/*消息通知*/}
        {msgList && msgList.length > 0 && (
          <div
            className="w-full h-[100px] flex items-center my-2"
            onClick={() => navigateTo({ url: '/packages/message/list/index' })}
            style={{
              background: `url(${messagebg}) no-repeat`,
              backgroundSize: 'contain',
              backgroundPosition: 'center',
            }}
          >
            <div className="pl-[140px] flex items-center">
              <div className="border-0 border-solid border-l-2 border-l-[#00000014] flex items-center">
                {msgList[0]?.msgStatus == MsgStatus.NoRead && (
                  <span className="h-[40px] ml-[12px] msgUnRead"></span>
                )}
                <span className="truncate w-[500px] ml-[12px] text-[26px] text-[#666666]">
                  {RegUtils.removeHtmlTag(msgList[0]?.content ?? '')}
                </span>
              </div>
              <ArrowRight color="#666666FF" width={12} height={20} />
            </div>
          </div>
        )}


        {/*实时概况统计区域*/}
        {/* <Card className="sale-amount-background">
          <div className="flex justify-end">
            <div className="text-[28px] font-light flex-grow flex justify-start items-center gap-[8px] text-[#FFFFFF]">
              <span>销售额</span>
              {!state ? (
                <IconFont name={Eye} onClick={toggle} />
              ) : (
                <IconFont name={EyeInvisible} onClick={toggle} />
              )}
            </div>
            <div className="flex flex-row-reverse text-[24px]">
              <Radio.Group
                defaultValue={'DAY'}
                shape="button"
                direction="horizontal"
                className="today-month"
                onChange={(value) => {
                  setDataType(value.toString());
                }}
              >
                <Radio value={'DAY'}>今日</Radio>
                <Radio value={'MONTH'}>本月</Radio>
              </Radio.Group>
            </div>
          </div>
          <div className="flex flex-grow items-left pb-[30px] text-[#FFFFFF]">
            <Price
              price={!state ? saleAmount?.[0] ?? '-' : '***'}
              digits={!state && saleAmount?.[0] ? 2 : 0}
              className="custom-price text-[48px]"
              symbol=""
              thousands
              size="normal"
              style={{
                '--nutui-price-integer-medium-size': '24px',
                '--nutui-price-decimal-medium-size': '24px',
              }}
            />
            <LastStatComponent
              title="销售额度"
              stat={saleAmount?.[1]}
              classnames="ml-[20px] pt-[18px] text-[26px]"
              state={state}
              dataType={dataType}
            />
          </div>

          <div className="flex justify text-[#FFFFFF]">
            <StatComponent
              title="销售毛利"
              stat={saleGrossProfit?.[0]}
              state={state}
              classnames="w-[35%]"
            />
            <StatComponent
              title="销售单数"
              stat={saleOrderNum?.[0]}
              state={state}
              classnames="ml-[10px] w-[35%]"
            />
            <StatComponent
              title="资金入账"
              stat={fundsReceived?.[0]}
              isDivider={false}
              state={state}
              classnames="ml-[10px] w-[30%]"
            />
          </div>
          <div className="flex justify mt-[12px] text-[#FFFFFF] text-[26px] font-light">
            <LastStatComponent
              stat={saleGrossProfit?.[1]}
              state={state}
              dataType={dataType}
              classnames="w-[35%]"
            />
            <LastStatComponent
              title="销售单数"
              stat={saleOrderNum?.[1]}
              classnames="ml-[10px] w-[35%]"
              state={state}
              dataType={dataType}
            />
            <LastStatComponent
              stat={fundsReceived?.[1]}
              classnames="ml-[10px] w-[30%]"
              state={state}
              dataType={dataType}
            />
          </div>
        </Card> */}



        {/*待办事项*/}
        <TodoComponent reloadFlag={isReloadFlag} />

        {/*销售趋势图表*/}
        {/* <div style={{ display: chooseStoreVisible ? 'none' : '' }}>
          <SaleTrendChartComponent storeId={storeId} isReloadFlag={isReloadFlag} />
        </div> */}
        <TaskComponent reloadFlag={isReloadFlag} />

      </PullToRefresh>

      <CustomTabBar activeTabIndex={0} />
    </>
  );
}
