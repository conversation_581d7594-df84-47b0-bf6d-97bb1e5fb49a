import Card from '@/components/Card';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import Collection from '@/pages/workbench/images/collection.png';
import Distribution from '@/pages/workbench/images/distribution.png';
import SalesInvoicing from '@/pages/workbench/images/sales_invoicing.png';
import Warehousing from '@/pages/workbench/images/warehousing.png';
import { Image } from '@tarojs/components';
import { navigateTo } from '@tarojs/taro';

const QuickActionsComponent = ({ title, items }) => {
  const icons = {
    销售开单: {
      imageUrl: SalesInvoicing,
      url: '/packages/sales/order/edit/index',
      permission: '/sales/order/edit/view',
    },
    库存管理: {
      imageUrl: Warehousing,
      url: '/packages/stocks/inventory/list/index',
      permission: '/stocks/inventory/view',
    },
    收款: {
      imageUrl: Collection,
      url: '/packages/finance/receive/list/index',
      permission: '/finance/receive/view',
    },
    配送任务: {
      imageUrl: Distribution,
      url: '/packages/stocks/delivery/list/index',
      // permission: '/packages/stocks/delivery/list/index',
    },
  };

  // 单个图标项组件
  const IconItem = ({ name, imageUrl, url, permission }) => (
    <PermissionComponent permission={permission}>
      <div className="flex flex-col items-center py-1" onClick={() => navigateTo({ url })}>
        <Image src={imageUrl} style={{ width: '28px', height: '28px' }} />
        <div className="text-center text-[28px] pt-[16px] text-[#666666]">{name}</div>
      </div>
    </PermissionComponent>
  );

  return (
    <Card title={title}>
      <div className="grid grid-cols-4 gap-4 items-center">
        {items.map((item, index) => (
          <IconItem
            key={index}
            name={item.name}
            imageUrl={icons[item.name]?.imageUrl}
            url={icons[item.name]?.url}
            permission={icons[item.name]?.permission}
          />
        ))}
      </div>
    </Card>
  );
};

export default QuickActionsComponent;
