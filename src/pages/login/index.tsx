import CustomNavBar from '@/components/CustomNavBar';
import { QueryPostListRequest } from '@/pages/login/types/query.post.list.request';
import usePermissionStore from '@/pages/splash/permissionStore';
import { menuListQueryPost } from '@/services/common';
import { Button, ConfigProvider, Input } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import { setStorageSync, showToast, switchTab, useLoad } from '@tarojs/taro';
import CryptoJS from 'crypto-js';
import { isEmpty } from 'lodash';
import { useRef, useState } from 'react';
import iconPassword from './images/iconPassword.png';
import iconPasswordOpen from './images/iconPasswordOpen.png';
import { loginPost } from './services';

export default function LoginForm() {
  const timeFun = useRef<NodeJS.Timer | null>(null);
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const permission = usePermissionStore();

  useLoad(() => {
    // @ts-ignore
    clearInterval(timeFun.current);
    return () => {
      // @ts-ignore
      clearInterval(timeFun.current);
    };
  });

  const [inputType, setInputType] = useState('password');

  const handleSubmit = async () => {
    try {
      //验证
      const values = { type: 0, channel: 1 } as QueryPostListRequest;
      if (isEmpty(email)) {
        showToast({ title: '请输入邮箱！', icon: 'none' });
        return;
      } else if (isEmpty(password)) {
        showToast({ title: '请输入密码！', icon: 'none' });
        return;
      } else {
        values.email = email;
        values.password = CryptoJS.MD5(password).toString();
      }
      const result = await loginPost(values);
      if (result) {
        const { sSessionId, accountId } = result;
        if (sSessionId) {
          setStorageSync('token', sSessionId);
        }
        //跳转到首页
        //showToast({ title: '登录成功！' + sSessionId, icon: 'none' });
        //加载权限
        const data = await menuListQueryPost({ type: '2' });
        if (data) {
          permission.setButtonItem(data);
        }
        switchTab({ url: '/pages/home/<USER>' });
        return;
      }
    } catch (error) {
      console.log(error);
      showToast({ title: '登录失败，请重试！', icon: 'none' });
    }
  };

  return (
    <div className="px-[48px]">
      <CustomNavBar title={''} showBack={false} />
      <div className="flex pt-[96px] items-center ">
        <div className="pr-[60px]">账户密码登录</div>
      </div>
      <ConfigProvider
        theme={{
          nutuiInputFontSize: '16px',
          nutuiInputBorderBottomWidth: '1px',
          nutuiInputPadding: '12px 3px',
          nutuiInputBackgroundColor: '#F5F5F5',
          nutuiDividerBorderColor: '#E1E1E1',
          nutuiDividerMargin: '0px',
        }}
      >
        <div className="pt-[60px]">
          <div>
            <Input placeholder="请输入邮箱" type="email" onChange={setEmail} key="email" />
          </div>
          <div className="pt-[24px] flex items-center relative">
            <Input
              type={inputType}
              placeholder="请输入密码"
              onChange={setPassword}
              maxLength={16}
              key="password"
            />
            <div
              className="right-0 absolute"
              style={{ zIndex: 10000 }}
              onClick={() => setInputType(inputType == 'text' ? 'password' : 'text')}
            >
              {inputType == 'text' ? (
                <Image src={iconPasswordOpen} style={{ width: '25px', height: '25px' }} />
              ) : (
                <Image src={iconPassword} style={{ width: '25px', height: '25px' }} />
              )}
            </div>
          </div>
        </div>
      </ConfigProvider>
      <div className="pt-[60px]">
        <Button
          block
          type="primary"
          style={{ marginBottom: '28px', borderRadius: '4px' }}
          size="large"
          shape="square"
          onClick={handleSubmit}
        >
          登录
        </Button>
      </div>
    </div>
  );
}
