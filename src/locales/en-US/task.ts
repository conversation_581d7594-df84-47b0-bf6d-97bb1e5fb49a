export default {
  // Page title and navigation
  'task.list.title': 'Todo Tasks',

  // Search and filter
  'task.list.search.placeholder': 'Task Description',
  'task.list.filter.todoPerson': 'Assignee',
  'task.list.filter.status': 'Status',

  // Status enum
  'task.status.notCompleted': 'Not Completed',

  // Task item display
  'task.item.todoPerson': 'Assignee',
  'task.item.completionTime': 'Completion Time',

  // Dialogs and confirmations
  'task.dialog.cancel.content': 'Are you sure to cancel?',

  // Toast messages
  'task.message.taskCompleted': 'Task Completed',
  'task.message.cancelSuccess': 'Cancel Success',

  // Edit task modal
  'task.edit.title.create': 'Create Task',
  'task.edit.title.edit': 'Edit Task',
  'task.edit.taskDesc.label': 'Task Description',
  'task.edit.taskDesc.placeholder': 'Please enter task description',
  'task.edit.taskDesc.required': '*',
  'task.edit.taskDesc.counter': '{current}/100',
  'task.edit.todoPerson.label': 'Assignee',

  // Complete task modal
  'task.complete.title.complete': 'Complete Task',
  'task.complete.title.view': 'Completion Note',
  'task.complete.desc.label': 'Completion Note',
  'task.complete.desc.counter': '{current}/100',
  'task.complete.desc.empty': '-',
  'task.complete.action.confirm': 'Confirm Complete',
}