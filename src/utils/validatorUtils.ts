import { FormItemType } from "@/packages/customer/add";
import Taro from "@tarojs/taro";
import { isEmpty } from "lodash";

export const validateForm = (formItems: FormItemType<any>[]) => {
  let result = true;
  formItems.filter((item) => item.required).forEach((item) => {
    console.log(item, isEmpty(item.value));
    if (isEmpty(item.value)) {
      Taro.showToast({ icon: 'none', title: `${item.label}为必填项！` });
      result = false;
    }
  });
  return result;
};