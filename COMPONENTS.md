## 组件用法手册（src/components）

本手册梳理了 `src/components` 下的所有通用组件，按功能分组说明用途、关键 Props 与典型用法，便于在业务页面快速接入。

- 更新时间：自动生成于当前代码库
- 技术栈：React + Taro + NutUI

### 使用约定
- 弹层/选择器类组件多为受控：父级传 `visible`/`onClose`，选择后 `onConfirm` 回调并关闭。
- 异步数据一般在 `useEffect`/`useAsyncEffect` 触发；确保必要入参（如门店权限）已就绪。
- 搜索类组件通常通过回调返回键值对，如：`inputValue({ [key]: value })`。

---

### 目录
- 选择器 / 弹窗选择
- 展示 / 信息卡片
- 搜索 / 输入控件
- 列表 / 分页
- 日期 / 日历
- 导航 / UI 框架

---

### 选择器 / 弹窗选择

- CustomPicker（`src/components/CustomPicker/index.tsx`）
  - 用途：底部弹窗列表选择，支持搜索、单选/多选、可配置是否需要“确定”按钮。
  - 关键 props：
    - `visible`、`onClose`、`onConfirm(selected: string[])`
    - `items: { title: string; value: string }[]`
    - `selected?: string[]`、`multiple?: boolean`、`needConfirm?: boolean`
    - `placeholder?`、`title?`
  - 典型用法：
    ```tsx
    <CustomPicker
      visible={visible}
      title="选择门店"
      placeholder="关键词"
      items={list}
      selected={selected}
      multiple={false}
      onConfirm={(vals) => setSelected(vals)}
      onClose={() => setVisible(false)}
    />
    ```

- CustomerTagPicker（`src/components/CustomerTagPicker/index.tsx`）
  - 用途：客户标签多选弹层。
  - 关键 props：`visible`、`onClose`、`onConfirm(selected)`、`items`、`selected`

- CustAccountSelectModal（`src/components/CustAccountSelectModal/index.tsx`）
  - 用途：账户选择弹层（基于 CustomPicker），单选。
  - 关键 props：`visibleAccount`、`belongToStore`、`handleClose`、`handleConfirm(options)`、`title?`、`placeholder?`
  - 要点：`visibleAccount` 变化触发加载；单选但回调为对象数组。

- AccountSelectModal（`src/components/AccountSelectModal/index.tsx`）
  - 用途：账户选择弹窗（自绘列表），单选，支持搜索。
  - 关键 props：`visibleAccount`、`accountId?`、`belongToStore?`、`handleClose`、`handleConfirm(option)`
  - 要点：未传 `accountId` 时默认选第一个并回调。

- ChooseCstModal（`src/components/ChooseCstModal/index.tsx`）
  - 用途：客户选择弹窗（搜索 + 列表项 `CstItem`）。
  - 关键 props：`visible`、`onClose`、`cstId?`、`onConfirm(record)`

- ChooseSupplierModal（`src/components/ChooseSupplierModal/index.tsx`）
  - 用途：供应商选择弹窗（搜索 + 应付金额汇总）。
  - 关键 props：`visible`、`onClose`、`supplierId?`、`onConfirm(record)`
  - 要点：内部先查供应商，再查应付分组并合并数据展示。

- ChooseWarehouse（`src/components/ChooseWarehouse/index.tsx`）
  - 用途：仓库选择（NutUI Picker）。
  - 关键 props：`handleClose?`、`handleConfirm?(options,value)` 及 `PickerProps`

- ChooseStoreAndWarehouseModal（`src/components/ChooseStoreAndWarehouseModal/index.tsx`）
  - 用途：二级联动门店-仓库选择（Picker）。
  - 关键 props：`value?: [storeId, warehouseId]`、`disabledColumns?: [boolean,boolean]`、`handleConfirm`、`handleClose`
  - 要点：加载门店/仓库并设置默认值；首次初始化会主动回调一次。

- ChooseStoreAndWarehouse（`src/components/ChooseStoreAndWarehouse/index.tsx`）
  - 用途：外层展示与触发器，显示“请选择门店与仓库/已选文案”。
  - 关键 props：同 Modal，另有 `onConfirm`、`disabled?`
  - 典型用法：
    ```tsx
    <ChooseStoreAndWarehouse
      visible={visible}
      value={[storeId, warehouseId]}
      disabledColumns={[false, false]}
      onConfirm={(opts, val) => { setStoreId(val[0]); setWarehouseId(val[1]); }}
      handleClose={() => setVisible(false)}
    />
    ```

- FilterCustomerPicker（`src/components/FilterCustomerPicker/index.tsx`）
  - 用途：列表筛选客户（搜索 + 虚拟列表）。
  - 关键 props：`cstId?`、`onChange(cstId?)`

- FilterSupplierPicker（`src/components/FilterSupplierPicker/index.tsx`）
  - 用途：列表筛选供应商（搜索 + 虚拟列表）。
  - 关键 props：`supplierId?`、`onChange(supplierId?)`

- FilterAccountPicker（`src/components/FilterAccountPicker/index.tsx`）
  - 用途：列表筛选账户（搜索）。
  - 关键 props：`accountId?`、`onChange(accountId?)`、`label`

- CustomMultipleChoose（`src/components/CustomMultipleChoose/index.tsx`）
  - 用途：多组条件（单/多选）面板，可搜索、可横/竖布局。
  - 关键 props：`items: {label,keyStr,multiple?,item:{text,value}[]}[]`、`selected?`、`onConfirm(selectedMap)`、`isSearch?`、`layout?`
  - 要点：返回值是按 `keyStr` 分组的选中映射（单选为字符串，多选为数组）。

---

### 展示 / 信息卡片

- CstCard（`src/components/CstCard/index.tsx`）
  - 用途：客户信息卡，点击可选择客户；支持禁用与自动拉取详情。
  - 关键 props：`cstId?`、`disabled?`、`onConfirm(CustomerEntity)`
  - 要点：`disabled && cstId` 时自动查询并回调详情。

- SupplierCard（`src/components/SupplierCard/index.tsx`）
  - 用途：供应商信息卡，点击可选择供应商。
  - 关键 props：`currentSupplier?`、`onConfirm(record)`、`disable?`

- DetailList（`src/components/DetailList/index.tsx`）
  - 用途：标签-值列表，支持对齐/冒号/分割线/自定义宽度与颜色。
  - 关键 props：`dataSource`、`justified?`、`labelWidth?`、`colon?`、`divider?`、`labelColor?`

- Descriptions（`src/components/Descriptions/index.tsx`）
  - 用途：结构化字段展示；数组值用下拉形式处理。
  - 关键 props：`columns: {key,title,valueType?}[]`、`record?`、`emptyText?`
  - 要点：`valueType='array'` 使用 `CodeWithDivider` 展示。

- CodeWithDivider（`src/components/CodeWithDivider/index.tsx`）
  - 用途：展示多个值，仅首个展示；多于一个时点击弹窗查看全部。
  - 关键 props：`items?: string[]`、`title?`

- ItemsWithDivider（`src/components/ItemsWithDivider/index.tsx`）
  - 用途：行内字符串并以竖线分隔（自动跳过空值）。
  - 关键 props：`items?: (string|undefined)[]`、`className?`

- MutiCurrency（`src/components/MutiCurrency/index.tsx`）
  - 用途：多币种金额拼接显示（分号分隔）；导出 `CurrencyAmount`。
  - 关键 props：`amountList?: { amount?, currency?, currencySymbol? }[]`

- TimeFormat（`src/components/TimeFormat/index.tsx`）
  - 用途：时间/时间段格式化为 `DD/MM/YYYY`（可含时分秒）。
  - 组件：`TimeFormat({ time, showTime? })`、`TimeRangeFormat({ startTime, endTime })`

- CustTag（`src/components/CustTag/index.tsx`）
  - 用途：标签样式文本（样式参数暂未生效）。

- GoodsCard（`src/components/GoodsCard/index.tsx`）、Card（`src/components/Card/index.tsx`）
  - 用途：卡片容器。`Card` 带外边距/可点击；`GoodsCard` 更轻量。
  - 关键 props：`title?`、`className?`、`onClick?`

- BluePrintCell（`src/components/BluePrintCell/index.tsx`）
  - 用途：打印机设置入口 Cell，展示连接状态与设备名；点击跳转设置页。

---

### 搜索 / 输入控件

- GoodsSearchBar（`src/components/GoodsSearchBar/index.tsx`）
  - 用途：商品搜索输入，右侧可扫码，可带新增按钮。
  - 关键 props：`keyStr`、`inputValue({[keyStr]: value})`、`isShowScan?`、`placeholder?`、`addUrl?`

- CustomSearchBar（`src/components/CustomSearchBar/index.tsx`）
  - 用途：左侧字段切换 + 输入 + 可扫码 + 可新增。
  - 关键 props：`itemList: {key,name,scanShow?,placeholder?}[]`、`defaultItem`、`inputValue`、`isShowAdd?`、`addUrl?`

- RadioButtonGroup（`src/components/RadioButtonGroup/index.tsx`）
  - 用途：NutUI 单选组按钮样式封装，横向排列。
  - 关键 props：与 `RadioGroupProps` 基本一致 + `options`

- StableStepper（`src/components/StableStepper/index.tsx`）
  - 用途：稳定的步进器（- 输入 +），受控。
  - 关键 props：`value`、`min?`、`max?`、`step?`、`disabled?`、`onChange?`、`className?`、`style?`

- SubmitCheckboxGroup（`src/components/SubmitCheckboxGroup/index.tsx`）
  - 用途：静态两个勾选项（确认结算/一键入库）。
  - 注意：当前内部自改数组，不向外暴露勾选状态，若需受控/回调需改造。

---

### 列表 / 分页

- Pagination/FunPagination（`src/components/Pagination/FunPagination.tsx`）
  - 用途：无限滚动列表（含下拉刷新与上拉加载）。
  - 关键 props：`fetchData(params)=>Promise<T[]>`、`renderItem(item,index,len)`、`params`、`initialPage?`、`popupNode?`
  - 要点：监听 `params` 变化自动刷新；`hasMore` 由返回条数是否等于 10 判定。

---

### 日期 / 日历

- CalendarRangeCardChoose（`src/components/CalendarRangeCardChoose/index.tsx`）
  - 用途：卡片式日期区间选择（重置/确定）。
  - 关键 props：`value?: Date[]` 及 `CalendarCardProps`（去掉 `value`）。
  - 要点：点击确定仅在选择满两日时回调，否则 Toast 提示。

---

### 导航 / UI 框架

- CustomNavBar（`src/components/CustomNavBar/index.tsx`）
  - 用途：自定义顶部导航栏，含返回箭头。
  - 关键 props：`title`、`showBack?`

- CustomTabBar（`src/components/CustomTabBar/index.tsx`）
  - 用途：自定义底部 TabBar。
  - 关键 props：`activeTabIndex?`
  - 要点：点击项使用 `Taro.navigateTo({ url })`，页面底部需留出安全区占位。

---

### 常见集成示例

- 无限滚动列表
```tsx
import FunPagination from '@/components/Pagination/FunPagination';

<FunPagination
  fetchData={(p) => api.queryList(p)}
  params={{ pageNo: 1, pageSize: 10, keyword }}
  renderItem={(item) => <ItemRow data={item} />}
/>
```

- 步进器（受控）
```tsx
import StableStepper from '@/components/StableStepper';

<StableStepper value={qty} min={0} onChange={setQty} />
```

- 通用筛选条（可切换字段+扫码）
```tsx
import CustomSearchBar from '@/components/CustomSearchBar';

<CustomSearchBar
  itemList={[
    { key: 'goodsName', name: '名称', scanShow: false },
    { key: 'barcode', name: '条码', scanShow: true },
  ]}
  defaultItem={{ key: 'goodsName', name: '名称', scanShow: false }}
  inputValue={(kv) => setSearch(prev => ({ ...prev, ...kv }))}
  isShowAdd
  addUrl={() => Taro.navigateTo({ url: '/pages/goods/add' })}
/>
```

- 客户卡 + 选择客户弹层
```tsx
import CstCard from '@/components/CstCard';

<CstCard
  cstId={cstId}
  onConfirm={(cst) => setCstId(cst.base?.cstId)}
/>
```

---

如需导出为英文版、补充更详细的 Props 表、或添加 Story/示例页，请提出具体需求。